import {
  <PERSON><PERSON>ontainer,
  ProCard,
  ProTable,
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
  ProFormDigit,
  ProFormDatePicker,
  ProFormGroup,
} from "@ant-design/pro-components";
import {
  Button,
  message,
  Modal,
  Space,
  Tabs,
  Tag,
  Descriptions,
  Card,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PrinterOutlined,
  SearchOutlined,
  RedoOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import React, { useState, useRef } from "react";
import type { ActionType, ProColumns } from "@ant-design/pro-components";

// 定义数据类型
interface MedicalInvoice {
  id: string;
  invoiceNo: string;
  invoiceCode: string;
  patientName: string;
  patientId: string;
  visitNo: string;
  department: string;
  doctor: string;
  totalAmount: number;
  issueDate: string;
  status: string;
  paymentMethod: string;
  items: InvoiceItem[];
}

interface InvoiceItem {
  id: string;
  itemCode: string;
  itemName: string;
  category: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

interface NonTaxInvoice {
  id: string;
  invoiceNo: string;
  invoiceType: string;
  payerName: string;
  payerIdCard: string;
  amount: number;
  purpose: string;
  issueDate: string;
  status: string;
  operator: string;
}

const InvoiceIssuing: React.FC = () => {
  const [activeTab, setActiveTab] = useState("medical");
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [modalType, setModalType] = useState<"add" | "edit" | "reverse">("add");
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockMedicalInvoiceData: MedicalInvoice[] = [
    {
      id: "1",
      invoiceNo: "PJ20250110001",
      invoiceCode: "MZPJ001",
      patientName: "张三",
      patientId: "110101199001011234",
      visitNo: "MZ20250110001",
      department: "内科",
      doctor: "李医生",
      totalAmount: 125.5,
      issueDate: "2025-01-10 09:30:00",
      status: "正常",
      paymentMethod: "微信支付",
      items: [
        {
          id: "1",
          itemCode: "GHFEI001",
          itemName: "普通门诊挂号费",
          category: "挂号费",
          quantity: 1,
          unitPrice: 5.0,
          amount: 5.0,
        },
        {
          id: "2",
          itemCode: "JCFEI001",
          itemName: "血常规检查",
          category: "检查费",
          quantity: 1,
          unitPrice: 25.0,
          amount: 25.0,
        },
        {
          id: "3",
          itemCode: "YPFEI001",
          itemName: "阿莫西林胶囊",
          category: "药品费",
          quantity: 2,
          unitPrice: 47.75,
          amount: 95.5,
        },
      ],
    },
    {
      id: "2",
      invoiceNo: "PJ20250110002",
      invoiceCode: "MZPJ001",
      patientName: "李四",
      patientId: "110101199002021234",
      visitNo: "MZ20250110002",
      department: "外科",
      doctor: "王医生",
      totalAmount: 85.0,
      issueDate: "2025-01-10 10:15:00",
      status: "已冲红",
      paymentMethod: "支付宝",
      items: [
        {
          id: "4",
          itemCode: "GHFEI002",
          itemName: "专家门诊挂号费",
          category: "挂号费",
          quantity: 1,
          unitPrice: 15.0,
          amount: 15.0,
        },
        {
          id: "5",
          itemCode: "ZLFEI001",
          itemName: "伤口处理",
          category: "治疗费",
          quantity: 1,
          unitPrice: 70.0,
          amount: 70.0,
        },
      ],
    },
  ];

  const mockNonTaxInvoiceData: NonTaxInvoice[] = [
    {
      id: "1",
      invoiceNo: "FS20250110001",
      invoiceType: "非税收入票据",
      payerName: "张三",
      payerIdCard: "110101199001011234",
      amount: 500.0,
      purpose: "体检费",
      issueDate: "2025-01-10 14:00:00",
      status: "正常",
      operator: "财务人员A",
    },
  ];

  // 医疗票据列配置
  const medicalInvoiceColumns: ProColumns<MedicalInvoice>[] = [
    {
      title: "票据号码",
      dataIndex: "invoiceNo",
      key: "invoiceNo",
      width: 140,
    },
    {
      title: "患者姓名",
      dataIndex: "patientName",
      key: "patientName",
      width: 100,
    },
    {
      title: "就诊号",
      dataIndex: "visitNo",
      key: "visitNo",
      width: 120,
    },
    {
      title: "科室",
      dataIndex: "department",
      key: "department",
      width: 100,
    },
    {
      title: "医生",
      dataIndex: "doctor",
      key: "doctor",
      width: 100,
    },
    {
      title: "金额",
      dataIndex: "totalAmount",
      key: "totalAmount",
      width: 100,
      valueType: "money",
    },
    {
      title: "开具时间",
      dataIndex: "issueDate",
      key: "issueDate",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "支付方式",
      dataIndex: "paymentMethod",
      key: "paymentMethod",
      width: 100,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          正常: { color: "green", text: "正常" },
          已冲红: { color: "red", text: "已冲红" },
          已作废: { color: "gray", text: "已作废" },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: "操作",
      key: "action",
      width: 250,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<SearchOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<PrinterOutlined />}
            onClick={() => handlePrint(record.id)}
          >
            打印
          </Button>
          {record.status === "正常" && (
            <Button
              type="link"
              size="small"
              danger
              icon={<RedoOutlined />}
              onClick={() => handleReverse(record)}
            >
              冲红
            </Button>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 非税票据列配置
  const nonTaxInvoiceColumns: ProColumns<NonTaxInvoice>[] = [
    {
      title: "票据号码",
      dataIndex: "invoiceNo",
      key: "invoiceNo",
      width: 140,
    },
    {
      title: "票据类型",
      dataIndex: "invoiceType",
      key: "invoiceType",
      width: 120,
    },
    {
      title: "缴款人",
      dataIndex: "payerName",
      key: "payerName",
      width: 100,
    },
    {
      title: "身份证号",
      dataIndex: "payerIdCard",
      key: "payerIdCard",
      width: 150,
    },
    {
      title: "金额",
      dataIndex: "amount",
      key: "amount",
      width: 100,
      valueType: "money",
    },
    {
      title: "缴费事由",
      dataIndex: "purpose",
      key: "purpose",
      width: 120,
    },
    {
      title: "开具时间",
      dataIndex: "issueDate",
      key: "issueDate",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "操作员",
      dataIndex: "operator",
      key: "operator",
      width: 100,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          正常: { color: "green", text: "正常" },
          已冲红: { color: "red", text: "已冲红" },
          已作废: { color: "gray", text: "已作废" },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<PrinterOutlined />}
            onClick={() => handlePrint(record.id)}
          >
            打印
          </Button>
          {record.status === "正常" && (
            <Button
              type="link"
              size="small"
              danger
              icon={<RedoOutlined />}
              onClick={() => handleReverse(record)}
            >
              冲红
            </Button>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setModalType("add");
    setEditingRecord(null);
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setModalType("edit");
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleViewDetail = (record: MedicalInvoice) => {
    setEditingRecord(record);
    setDetailModalVisible(true);
  };

  const handleReverse = (record: any) => {
    Modal.confirm({
      title: "确认冲红",
      content: `确定要对票据 ${record.invoiceNo} 进行冲红操作吗？`,
      onOk: () => {
        message.success("冲红成功");
        actionRef.current?.reload();
      },
    });
  };

  const handlePrint = (id: string) => {
    message.success("打印任务已发送");
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这条记录吗？",
      onOk: () => {
        message.success("删除成功");
        actionRef.current?.reload();
      },
    });
  };

  const handleModalOk = async (values: any) => {
    try {
      if (modalType === "add") {
        message.success("开具成功");
      } else {
        message.success("编辑成功");
      }
      setModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error("操作失败");
    }
  };

  const renderModalForm = () => {
    const getModalTitle = () => {
      const typeMap = {
        medical: "医疗电子票据",
        nonTax: "非税收入票据",
      };
      const typeName = typeMap[activeTab as keyof typeof typeMap] || "票据";
      return modalType === "add" ? `开具${typeName}` : `编辑${typeName}`;
    };

    const commonModalProps = {
      title: getModalTitle(),
      open: modalVisible,
      onCancel: () => setModalVisible(false),
      width: 800,
      destroyOnClose: true,
      footer: null,
    };

    if (activeTab === "medical") {
      return (
        <Modal {...commonModalProps}>
          <ProForm
            initialValues={editingRecord}
            onFinish={handleModalOk}
            submitter={{
              searchConfig: {
                submitText: modalType === "add" ? "开具票据" : "保存",
                resetText: "取消",
              },
              resetButtonProps: {
                onClick: () => setModalVisible(false),
              },
            }}
          >
            <ProFormGroup title="基本信息">
              <ProFormText
                name="invoiceNo"
                label="票据号码"
                rules={[
                  { required: true, message: "请输入票据号码" },
                  {
                    pattern: /^PJ\d{11}$/,
                    message: "票据号码格式：PJ + 11位数字",
                  },
                ]}
                placeholder="请输入票据号码，如：PJ20250110001"
                disabled={modalType === "edit"}
              />
              <ProFormText
                name="invoiceCode"
                label="票据代码"
                rules={[
                  { required: true, message: "请输入票据代码" },
                  {
                    pattern: /^[A-Z0-9]{6,10}$/,
                    message: "票据代码应为6-10位大写字母和数字组合",
                  },
                ]}
                placeholder="请输入票据代码，如：MZPJ001"
              />
            </ProFormGroup>
            <ProFormGroup title="患者信息">
              <ProFormText
                name="patientName"
                label="患者姓名"
                rules={[
                  { required: true, message: "请输入患者姓名" },
                  { max: 20, message: "患者姓名不能超过20个字符" },
                ]}
                placeholder="请输入患者姓名"
              />
              <ProFormText
                name="patientId"
                label="身份证号"
                rules={[
                  { required: true, message: "请输入身份证号" },
                  {
                    pattern:
                      /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
                    message: "请输入正确的身份证号",
                  },
                ]}
                placeholder="请输入18位身份证号"
              />
              <ProFormText
                name="visitNo"
                label="就诊号"
                rules={[
                  { required: true, message: "请输入就诊号" },
                  {
                    pattern: /^[A-Z]{2}\d{11}$/,
                    message: "就诊号格式：2位字母 + 11位数字",
                  },
                ]}
                placeholder="请输入就诊号，如：MZ20250110001"
              />
            </ProFormGroup>
            <ProFormGroup title="医疗信息">
              <ProFormSelect
                name="department"
                label="科室"
                options={[
                  { label: "内科", value: "内科" },
                  { label: "外科", value: "外科" },
                  { label: "妇科", value: "妇科" },
                  { label: "儿科", value: "儿科" },
                  { label: "急诊科", value: "急诊科" },
                ]}
                rules={[{ required: true, message: "请选择科室" }]}
                placeholder="请选择科室"
              />
              <ProFormText
                name="doctor"
                label="医生"
                rules={[
                  { required: true, message: "请输入医生姓名" },
                  { max: 20, message: "医生姓名不能超过20个字符" },
                ]}
                placeholder="请输入医生姓名"
              />
            </ProFormGroup>
            <ProFormGroup title="费用信息">
              <ProFormDigit
                name="totalAmount"
                label="总金额"
                rules={[
                  { required: true, message: "请输入总金额" },
                  { type: "number", min: 0, message: "总金额不能为负数" },
                ]}
                placeholder="请输入总金额"
                fieldProps={{
                  precision: 2,
                  min: 0,
                  max: 999999.99,
                  addonAfter: "元",
                }}
              />
              <ProFormSelect
                name="paymentMethod"
                label="支付方式"
                options={[
                  { label: "现金", value: "现金" },
                  { label: "微信支付", value: "微信支付" },
                  { label: "支付宝", value: "支付宝" },
                  { label: "银行卡", value: "银行卡" },
                  { label: "医保", value: "医保" },
                ]}
                rules={[{ required: true, message: "请选择支付方式" }]}
                placeholder="请选择支付方式"
              />
            </ProFormGroup>
            <ProFormDatePicker
              name="issueDate"
              label="开具时间"
              rules={[{ required: true, message: "请选择开具时间" }]}
              fieldProps={{
                showTime: true,
                format: "YYYY-MM-DD HH:mm:ss",
              }}
            />
          </ProForm>
        </Modal>
      );
    } else if (activeTab === "nonTax") {
      return (
        <Modal {...commonModalProps}>
          <ProForm
            initialValues={editingRecord}
            onFinish={handleModalOk}
            submitter={{
              searchConfig: {
                submitText: modalType === "add" ? "开具票据" : "保存",
                resetText: "取消",
              },
              resetButtonProps: {
                onClick: () => setModalVisible(false),
              },
            }}
          >
            <ProFormText
              name="invoiceNo"
              label="票据号码"
              rules={[
                { required: true, message: "请输入票据号码" },
                {
                  pattern: /^FS\d{11}$/,
                  message: "票据号码格式：FS + 11位数字",
                },
              ]}
              placeholder="请输入票据号码，如：FS20250110001"
              disabled={modalType === "edit"}
            />
            <ProFormSelect
              name="invoiceType"
              label="票据类型"
              options={[
                { label: "非税收入票据", value: "非税收入票据" },
                { label: "往来结算票据", value: "往来结算票据" },
                { label: "捐赠票据", value: "捐赠票据" },
              ]}
              rules={[{ required: true, message: "请选择票据类型" }]}
              placeholder="请选择票据类型"
            />
            <ProFormText
              name="payerName"
              label="缴款人"
              rules={[
                { required: true, message: "请输入缴款人姓名" },
                { max: 50, message: "缴款人姓名不能超过50个字符" },
              ]}
              placeholder="请输入缴款人姓名"
            />
            <ProFormText
              name="payerIdCard"
              label="身份证号"
              rules={[
                { required: true, message: "请输入身份证号" },
                {
                  pattern:
                    /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
                  message: "请输入正确的身份证号",
                },
              ]}
              placeholder="请输入18位身份证号"
            />
            <ProFormDigit
              name="amount"
              label="金额"
              rules={[
                { required: true, message: "请输入金额" },
                { type: "number", min: 0, message: "金额不能为负数" },
              ]}
              placeholder="请输入金额"
              fieldProps={{
                precision: 2,
                min: 0,
                max: 999999.99,
                addonAfter: "元",
              }}
            />
            <ProFormText
              name="purpose"
              label="缴费事由"
              rules={[
                { required: true, message: "请输入缴费事由" },
                { max: 100, message: "缴费事由不能超过100个字符" },
              ]}
              placeholder="请输入缴费事由"
            />
            <ProFormDatePicker
              name="issueDate"
              label="开具时间"
              rules={[{ required: true, message: "请选择开具时间" }]}
              fieldProps={{
                showTime: true,
                format: "YYYY-MM-DD HH:mm:ss",
              }}
            />
            <ProFormText
              name="operator"
              label="操作员"
              rules={[
                { required: true, message: "请输入操作员" },
                { max: 20, message: "操作员姓名不能超过20个字符" },
              ]}
              placeholder="请输入操作员姓名"
            />
          </ProForm>
        </Modal>
      );
    }

    return null;
  };

  const renderDetailModal = () => {
    if (!editingRecord) return null;

    return (
      <Modal
        title="票据详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="print" type="primary" icon={<PrinterOutlined />}>
            打印票据
          </Button>,
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        <Card>
          <Descriptions title="基本信息" bordered column={2}>
            <Descriptions.Item label="票据号码">
              {editingRecord.invoiceNo}
            </Descriptions.Item>
            <Descriptions.Item label="票据代码">
              {editingRecord.invoiceCode}
            </Descriptions.Item>
            <Descriptions.Item label="患者姓名">
              {editingRecord.patientName}
            </Descriptions.Item>
            <Descriptions.Item label="身份证号">
              {editingRecord.patientId}
            </Descriptions.Item>
            <Descriptions.Item label="就诊号">
              {editingRecord.visitNo}
            </Descriptions.Item>
            <Descriptions.Item label="科室">
              {editingRecord.department}
            </Descriptions.Item>
            <Descriptions.Item label="医生">
              {editingRecord.doctor}
            </Descriptions.Item>
            <Descriptions.Item label="开具时间">
              {editingRecord.issueDate}
            </Descriptions.Item>
            <Descriptions.Item label="支付方式">
              {editingRecord.paymentMethod}
            </Descriptions.Item>
            <Descriptions.Item label="总金额">
              <span
                style={{ color: "#f50", fontSize: "16px", fontWeight: "bold" }}
              >
                ¥{editingRecord.totalAmount}
              </span>
            </Descriptions.Item>
          </Descriptions>

          <div style={{ marginTop: 24 }}>
            <h4>收费明细</h4>
            <ProTable
              columns={[
                { title: "项目代码", dataIndex: "itemCode", width: 100 },
                { title: "项目名称", dataIndex: "itemName", width: 150 },
                { title: "分类", dataIndex: "category", width: 100 },
                {
                  title: "数量",
                  dataIndex: "quantity",
                  width: 80,
                  valueType: "digit",
                },
                {
                  title: "单价",
                  dataIndex: "unitPrice",
                  width: 100,
                  valueType: "money",
                },
                {
                  title: "金额",
                  dataIndex: "amount",
                  width: 100,
                  valueType: "money",
                },
              ]}
              dataSource={editingRecord.items}
              rowKey="id"
              search={false}
              pagination={false}
              toolBarRender={false}
              size="small"
            />
          </div>
        </Card>
      </Modal>
    );
  };

  const tabItems = [
    {
      key: "medical",
      label: "医疗电子票据",
      children: (
        <ProTable<MedicalInvoice>
          columns={medicalInvoiceColumns}
          dataSource={mockMedicalInvoiceData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: "auto",
          }}
          scroll={{ x: "max-content" }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              手动开票
            </Button>,
            <Button key="sync" icon={<FileTextOutlined />}>
              同步HIS数据
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "nonTax",
      label: "非税收入票据",
      children: (
        <ProTable<NonTaxInvoice>
          columns={nonTaxInvoiceColumns}
          dataSource={mockNonTaxInvoiceData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: "auto",
          }}
          scroll={{ x: "max-content" }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              开具票据
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
  ];

  return (
    <PageContainer
      title="票据开具"
      content="开具医疗电子票据、非税收入票据等各类票据，支持票据冲红、打印和查询功能"
    >
      <ProCard>
        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
      </ProCard>
      {renderDetailModal()}
      {renderModalForm()}
    </PageContainer>
  );
};

export default InvoiceIssuing;
