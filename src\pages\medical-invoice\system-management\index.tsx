import {
  <PERSON><PERSON><PERSON>r,
  ProCard,
  ProTable,
  ProForm,
  ProFormText,
  ProFormSelect,
  ProFormSwitch,
  ProFormTextArea,
} from "@ant-design/pro-components";
import { Button, message, Modal, Space, Tabs, Tag, Tree, Transfer } from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  TeamOutlined,
  SettingOutlined,
  ApiOutlined,
  KeyOutlined,
  SafetyOutlined,
} from "@ant-design/icons";
import React, { useState, useRef } from "react";
import type { ActionType, ProColumns } from "@ant-design/pro-components";

// 定义数据类型
interface User {
  id: string;
  username: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  roles: string[];
  status: string;
  lastLoginTime: string;
  createTime: string;
}

interface Role {
  id: string;
  roleName: string;
  roleCode: string;
  description: string;
  permissions: string[];
  userCount: number;
  status: string;
  createTime: string;
}

interface Application {
  id: string;
  appName: string;
  appCode: string;
  appType: string;
  apiKey: string;
  secretKey: string;
  status: string;
  lastAccessTime: string;
  createTime: string;
  description: string;
}

const SystemManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState("users");
  const [modalVisible, setModalVisible] = useState(false);
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [modalType, setModalType] = useState<"add" | "edit" | "permission">(
    "add"
  );
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockUserData: User[] = [
    {
      id: "1",
      username: "admin",
      realName: "系统管理员",
      email: "<EMAIL>",
      phone: "13800138001",
      department: "信息科",
      roles: ["超级管理员"],
      status: "正常",
      lastLoginTime: "2025-01-10 09:00:00",
      createTime: "2024-01-01 10:00:00",
    },
    {
      id: "2",
      username: "finance01",
      realName: "张财务",
      email: "<EMAIL>",
      phone: "13800138002",
      department: "财务科",
      roles: ["财务管理员", "票据操作员"],
      status: "正常",
      lastLoginTime: "2025-01-10 08:30:00",
      createTime: "2024-01-15 14:00:00",
    },
  ];

  const mockRoleData: Role[] = [
    {
      id: "1",
      roleName: "超级管理员",
      roleCode: "SUPER_ADMIN",
      description: "系统超级管理员，拥有所有权限",
      permissions: ["user:manage", "role:manage", "invoice:all", "report:all"],
      userCount: 1,
      status: "正常",
      createTime: "2024-01-01 10:00:00",
    },
    {
      id: "2",
      roleName: "财务管理员",
      roleCode: "FINANCE_ADMIN",
      description: "财务部门管理员",
      permissions: ["invoice:manage", "report:view", "basic:manage"],
      userCount: 3,
      status: "正常",
      createTime: "2024-01-01 10:00:00",
    },
  ];

  const mockApplicationData: Application[] = [
    {
      id: "1",
      appName: "HIS系统",
      appCode: "HIS_SYSTEM",
      appType: "医院信息系统",
      apiKey: "ak_his_20250101",
      secretKey: "sk_his_***hidden***",
      status: "正常",
      lastAccessTime: "2025-01-10 09:30:00",
      createTime: "2024-01-01 10:00:00",
      description: "医院信息系统对接",
    },
    {
      id: "2",
      appName: "财政系统",
      appCode: "FINANCE_SYSTEM",
      appType: "财政电子票据系统",
      apiKey: "ak_finance_20250101",
      secretKey: "sk_finance_***hidden***",
      status: "正常",
      lastAccessTime: "2025-01-10 08:00:00",
      createTime: "2024-01-01 10:00:00",
      description: "财政电子票据系统对接",
    },
  ];

  // 权限树数据
  const permissionTreeData = [
    {
      title: "用户管理",
      key: "user",
      children: [
        { title: "用户查看", key: "user:view" },
        { title: "用户管理", key: "user:manage" },
        { title: "密码重置", key: "user:reset" },
      ],
    },
    {
      title: "角色管理",
      key: "role",
      children: [
        { title: "角色查看", key: "role:view" },
        { title: "角色管理", key: "role:manage" },
        { title: "权限配置", key: "role:permission" },
      ],
    },
    {
      title: "票据管理",
      key: "invoice",
      children: [
        { title: "票据查看", key: "invoice:view" },
        { title: "票据管理", key: "invoice:manage" },
        { title: "票据开具", key: "invoice:issue" },
        { title: "票据冲红", key: "invoice:reverse" },
      ],
    },
    {
      title: "报表查询",
      key: "report",
      children: [
        { title: "报表查看", key: "report:view" },
        { title: "报表导出", key: "report:export" },
        { title: "统计分析", key: "report:analysis" },
      ],
    },
  ];

  // 用户管理列配置
  const userColumns: ProColumns<User>[] = [
    {
      title: "用户名",
      dataIndex: "username",
      key: "username",
      width: 120,
    },
    {
      title: "真实姓名",
      dataIndex: "realName",
      key: "realName",
      width: 120,
    },
    {
      title: "邮箱",
      dataIndex: "email",
      key: "email",
      width: 180,
    },
    {
      title: "手机号",
      dataIndex: "phone",
      key: "phone",
      width: 120,
    },
    {
      title: "部门",
      dataIndex: "department",
      key: "department",
      width: 100,
    },
    {
      title: "角色",
      dataIndex: "roles",
      key: "roles",
      width: 150,
      render: (_, record) => (
        <Space>
          {record.roles.map((role) => (
            <Tag key={role} color="blue">
              {role}
            </Tag>
          ))}
        </Space>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (_, record) => (
        <Tag color={record.status === "正常" ? "green" : "red"}>
          {record.status}
        </Tag>
      ),
    },
    {
      title: "最后登录",
      dataIndex: "lastLoginTime",
      key: "lastLoginTime",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<KeyOutlined />}
            onClick={() => handleResetPassword(record.id)}
          >
            重置密码
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 角色管理列配置
  const roleColumns: ProColumns<Role>[] = [
    {
      title: "角色名称",
      dataIndex: "roleName",
      key: "roleName",
      width: 150,
    },
    {
      title: "角色代码",
      dataIndex: "roleCode",
      key: "roleCode",
      width: 150,
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
      width: 200,
      ellipsis: true,
    },
    {
      title: "用户数量",
      dataIndex: "userCount",
      key: "userCount",
      width: 100,
      valueType: "digit",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (_, record) => (
        <Tag color={record.status === "正常" ? "green" : "red"}>
          {record.status}
        </Tag>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<SafetyOutlined />}
            onClick={() => handlePermissionConfig(record)}
          >
            权限配置
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 应用接入列配置
  const applicationColumns: ProColumns<Application>[] = [
    {
      title: "应用名称",
      dataIndex: "appName",
      key: "appName",
      width: 150,
    },
    {
      title: "应用代码",
      dataIndex: "appCode",
      key: "appCode",
      width: 150,
    },
    {
      title: "应用类型",
      dataIndex: "appType",
      key: "appType",
      width: 150,
    },
    {
      title: "API Key",
      dataIndex: "apiKey",
      key: "apiKey",
      width: 150,
      ellipsis: true,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (_, record) => (
        <Tag color={record.status === "正常" ? "green" : "red"}>
          {record.status}
        </Tag>
      ),
    },
    {
      title: "最后访问",
      dataIndex: "lastAccessTime",
      key: "lastAccessTime",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<KeyOutlined />}
            onClick={() => handleRegenerateKey(record.id)}
          >
            重新生成密钥
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setModalType("add");
    setEditingRecord(null);
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setModalType("edit");
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这条记录吗？",
      onOk: () => {
        message.success("删除成功");
        actionRef.current?.reload();
      },
    });
  };

  const handleResetPassword = (id: string) => {
    Modal.confirm({
      title: "确认重置密码",
      content: "确定要重置该用户的密码吗？新密码将发送到用户邮箱。",
      onOk: () => {
        message.success("密码重置成功，新密码已发送到用户邮箱");
      },
    });
  };

  const handlePermissionConfig = (record: Role) => {
    setEditingRecord(record);
    setPermissionModalVisible(true);
  };

  const handleRegenerateKey = (id: string) => {
    Modal.confirm({
      title: "确认重新生成密钥",
      content: "确定要重新生成API密钥吗？旧密钥将立即失效。",
      onOk: () => {
        message.success("API密钥重新生成成功");
        actionRef.current?.reload();
      },
    });
  };

  const handleModalOk = async (values: any) => {
    try {
      if (modalType === "add") {
        message.success("新增成功");
      } else {
        message.success("编辑成功");
      }
      setModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error("操作失败");
    }
  };

  const renderModalForm = () => {
    const getModalTitle = () => {
      const typeMap = {
        users: "用户",
        roles: "角色",
        applications: "应用",
      };
      const typeName = typeMap[activeTab as keyof typeof typeMap] || "信息";
      return modalType === "add" ? `新增${typeName}` : `编辑${typeName}`;
    };

    const commonModalProps = {
      title: getModalTitle(),
      open: modalVisible,
      onCancel: () => setModalVisible(false),
      width: 600,
      destroyOnClose: true,
      footer: null,
    };

    if (activeTab === "users") {
      return (
        <Modal {...commonModalProps}>
          <ProForm
            initialValues={editingRecord}
            onFinish={handleModalOk}
            submitter={{
              searchConfig: {
                submitText: modalType === "add" ? "新增" : "保存",
                resetText: "取消",
              },
              resetButtonProps: {
                onClick: () => setModalVisible(false),
              },
            }}
          >
            <ProFormText
              name="username"
              label="用户名"
              rules={[
                { required: true, message: "请输入用户名" },
                {
                  pattern: /^[a-zA-Z0-9_]{3,20}$/,
                  message: "用户名应为3-20位字母、数字或下划线",
                },
              ]}
              placeholder="请输入用户名"
              disabled={modalType === "edit"}
            />
            <ProFormText
              name="realName"
              label="真实姓名"
              rules={[
                { required: true, message: "请输入真实姓名" },
                { max: 20, message: "真实姓名不能超过20个字符" },
              ]}
              placeholder="请输入真实姓名"
            />
            <ProFormText
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: "请输入邮箱" },
                { type: "email", message: "请输入正确的邮箱格式" },
              ]}
              placeholder="请输入邮箱地址"
            />
            <ProFormText
              name="phone"
              label="手机号"
              rules={[
                { required: true, message: "请输入手机号" },
                { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号" },
              ]}
              placeholder="请输入11位手机号"
            />
            <ProFormSelect
              name="department"
              label="部门"
              options={[
                { label: "信息科", value: "信息科" },
                { label: "财务科", value: "财务科" },
                { label: "门诊部", value: "门诊部" },
                { label: "住院部", value: "住院部" },
                { label: "行政办", value: "行政办" },
              ]}
              rules={[{ required: true, message: "请选择部门" }]}
              placeholder="请选择部门"
            />
            <ProFormSelect
              name="roles"
              label="角色"
              mode="multiple"
              options={[
                { label: "超级管理员", value: "超级管理员" },
                { label: "财务管理员", value: "财务管理员" },
                { label: "票据操作员", value: "票据操作员" },
                { label: "普通用户", value: "普通用户" },
              ]}
              rules={[{ required: true, message: "请选择角色" }]}
              placeholder="请选择用户角色"
            />
            <ProFormSelect
              name="status"
              label="状态"
              options={[
                { label: "正常", value: "正常" },
                { label: "停用", value: "停用" },
              ]}
              rules={[{ required: true, message: "请选择状态" }]}
              placeholder="请选择状态"
            />
          </ProForm>
        </Modal>
      );
    } else if (activeTab === "roles") {
      return (
        <Modal {...commonModalProps}>
          <ProForm
            initialValues={editingRecord}
            onFinish={handleModalOk}
            submitter={{
              searchConfig: {
                submitText: modalType === "add" ? "新增" : "保存",
                resetText: "取消",
              },
              resetButtonProps: {
                onClick: () => setModalVisible(false),
              },
            }}
          >
            <ProFormText
              name="roleName"
              label="角色名称"
              rules={[
                { required: true, message: "请输入角色名称" },
                { max: 50, message: "角色名称不能超过50个字符" },
              ]}
              placeholder="请输入角色名称"
            />
            <ProFormText
              name="roleCode"
              label="角色代码"
              rules={[
                { required: true, message: "请输入角色代码" },
                {
                  pattern: /^[A-Z_]{3,20}$/,
                  message: "角色代码应为3-20位大写字母和下划线",
                },
              ]}
              placeholder="请输入角色代码，如：FINANCE_ADMIN"
              disabled={modalType === "edit"}
            />
            <ProFormTextArea
              name="description"
              label="角色描述"
              rules={[
                { required: true, message: "请输入角色描述" },
                { max: 200, message: "角色描述不能超过200个字符" },
              ]}
              placeholder="请输入角色描述"
              fieldProps={{ rows: 3 }}
            />
            <ProFormSelect
              name="status"
              label="状态"
              options={[
                { label: "正常", value: "正常" },
                { label: "停用", value: "停用" },
              ]}
              rules={[{ required: true, message: "请选择状态" }]}
              placeholder="请选择状态"
            />
          </ProForm>
        </Modal>
      );
    } else if (activeTab === "applications") {
      return (
        <Modal {...commonModalProps}>
          <ProForm
            initialValues={editingRecord}
            onFinish={handleModalOk}
            submitter={{
              searchConfig: {
                submitText: modalType === "add" ? "新增" : "保存",
                resetText: "取消",
              },
              resetButtonProps: {
                onClick: () => setModalVisible(false),
              },
            }}
          >
            <ProFormText
              name="appName"
              label="应用名称"
              rules={[
                { required: true, message: "请输入应用名称" },
                { max: 50, message: "应用名称不能超过50个字符" },
              ]}
              placeholder="请输入应用名称"
            />
            <ProFormText
              name="appCode"
              label="应用代码"
              rules={[
                { required: true, message: "请输入应用代码" },
                {
                  pattern: /^[A-Z_]{3,20}$/,
                  message: "应用代码应为3-20位大写字母和下划线",
                },
              ]}
              placeholder="请输入应用代码，如：HIS_SYSTEM"
              disabled={modalType === "edit"}
            />
            <ProFormSelect
              name="appType"
              label="应用类型"
              options={[
                { label: "医院信息系统", value: "医院信息系统" },
                { label: "财政电子票据系统", value: "财政电子票据系统" },
                { label: "第三方支付系统", value: "第三方支付系统" },
                { label: "移动应用", value: "移动应用" },
                { label: "其他系统", value: "其他系统" },
              ]}
              rules={[{ required: true, message: "请选择应用类型" }]}
              placeholder="请选择应用类型"
            />
            <ProFormTextArea
              name="description"
              label="应用描述"
              rules={[
                { required: true, message: "请输入应用描述" },
                { max: 200, message: "应用描述不能超过200个字符" },
              ]}
              placeholder="请输入应用描述"
              fieldProps={{ rows: 3 }}
            />
            <ProFormSelect
              name="status"
              label="状态"
              options={[
                { label: "正常", value: "正常" },
                { label: "停用", value: "停用" },
              ]}
              rules={[{ required: true, message: "请选择状态" }]}
              placeholder="请选择状态"
            />
          </ProForm>
        </Modal>
      );
    }

    return null;
  };

  const renderPermissionModal = () => (
    <Modal
      title="权限配置"
      open={permissionModalVisible}
      onCancel={() => setPermissionModalVisible(false)}
      onOk={() => {
        message.success("权限配置保存成功");
        setPermissionModalVisible(false);
      }}
      width={600}
    >
      <div style={{ marginBottom: 16 }}>
        <strong>角色：{editingRecord?.roleName}</strong>
      </div>
      <Tree
        checkable
        defaultExpandAll
        defaultCheckedKeys={editingRecord?.permissions || []}
        treeData={permissionTreeData}
      />
    </Modal>
  );

  const tabItems = [
    {
      key: "users",
      label: "用户管理",
      children: (
        <ProTable<User>
          columns={userColumns}
          dataSource={mockUserData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: "auto",
          }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增用户
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "roles",
      label: "角色管理",
      children: (
        <ProTable<Role>
          columns={roleColumns}
          dataSource={mockRoleData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: "auto",
          }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增角色
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "applications",
      label: "应用接入管理",
      children: (
        <ProTable<Application>
          columns={applicationColumns}
          dataSource={mockApplicationData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: "auto",
          }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增应用
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
  ];

  return (
    <PageContainer
      title="系统管理"
      content="管理系统用户、角色权限和外部应用接入，确保系统安全稳定运行"
    >
      <ProCard>
        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
      </ProCard>
      {renderPermissionModal()}
      {renderModalForm()}
    </PageContainer>
  );
};

export default SystemManagement;
