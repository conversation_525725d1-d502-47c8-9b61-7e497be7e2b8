import {
  <PERSON><PERSON>ontainer,
  ProCard,
  ProTable,
  ProForm,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
} from "@ant-design/pro-components";
import {
  Button,
  message,
  Space,
  Tabs,
  Card,
  Row,
  Col,
  Statistic,
  DatePicker,
} from "antd";
import {
  DownloadOutlined,
  Bar<PERSON><PERSON>Outlined,
  Pie<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  FileExcelOutlined,
  FilePdfOutlined,
  PrinterOutlined,
} from "@ant-design/icons";
import React, { useState, useRef } from "react";
import type { ActionType, ProColumns } from "@ant-design/pro-components";
import { Column, Pie, Line } from "@ant-design/plots";

const { RangePicker } = DatePicker;

// 定义数据类型
interface InventoryReport {
  id: string;
  invoiceType: string;
  department: string;
  initialStock: number;
  received: number;
  issued: number;
  returned: number;
  currentStock: number;
  reportDate: string;
}

interface IssuingReport {
  id: string;
  department: string;
  invoiceType: string;
  issueCount: number;
  issueAmount: number;
  reverseCount: number;
  reverseAmount: number;
  netCount: number;
  netAmount: number;
  reportDate: string;
}

const ComprehensiveReports: React.FC = () => {
  const [activeTab, setActiveTab] = useState("inventory");
  const [dateRange, setDateRange] = useState<any>([]);
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockInventoryData: InventoryReport[] = [
    {
      id: "1",
      invoiceType: "门诊医疗电子票据",
      department: "门诊部",
      initialStock: 1000,
      received: 500,
      issued: 300,
      returned: 10,
      currentStock: 1190,
      reportDate: "2025-01-10",
    },
    {
      id: "2",
      invoiceType: "住院医疗电子票据",
      department: "住院部",
      initialStock: 800,
      received: 200,
      issued: 150,
      returned: 5,
      currentStock: 845,
      reportDate: "2025-01-10",
    },
  ];

  const mockIssuingData: IssuingReport[] = [
    {
      id: "1",
      department: "门诊部",
      invoiceType: "门诊医疗电子票据",
      issueCount: 150,
      issueAmount: 18750.5,
      reverseCount: 2,
      reverseAmount: 125.0,
      netCount: 148,
      netAmount: 18625.5,
      reportDate: "2025-01-10",
    },
    {
      id: "2",
      department: "住院部",
      invoiceType: "住院医疗电子票据",
      issueCount: 80,
      issueAmount: 125000.0,
      reverseCount: 1,
      reverseAmount: 1500.0,
      netCount: 79,
      netAmount: 123500.0,
      reportDate: "2025-01-10",
    },
  ];

  // 图表数据
  const chartData = {
    departmentIssue: [
      { department: "门诊部", count: 148, amount: 18625.5 },
      { department: "住院部", count: 79, amount: 123500.0 },
      { department: "急诊科", count: 45, amount: 5600.0 },
      { department: "体检中心", count: 32, amount: 8900.0 },
    ],
    invoiceTypeDistribution: [
      { type: "门诊医疗电子票据", value: 148 },
      { type: "住院医疗电子票据", value: 79 },
      { type: "非税收入票据", value: 25 },
      { type: "往来结算票据", value: 12 },
    ],
    dailyTrend: [
      { date: "2025-01-01", count: 120, amount: 15000 },
      { date: "2025-01-02", count: 135, amount: 18500 },
      { date: "2025-01-03", count: 98, amount: 12300 },
      { date: "2025-01-04", count: 156, amount: 21200 },
      { date: "2025-01-05", count: 142, amount: 19800 },
      { date: "2025-01-06", count: 167, amount: 23400 },
      { date: "2025-01-07", count: 189, amount: 26700 },
    ],
  };

  // 库存报表列配置
  const inventoryColumns: ProColumns<InventoryReport>[] = [
    {
      title: "票据类型",
      dataIndex: "invoiceType",
      key: "invoiceType",
      width: 150,
    },
    {
      title: "部门",
      dataIndex: "department",
      key: "department",
      width: 120,
    },
    {
      title: "期初库存",
      dataIndex: "initialStock",
      key: "initialStock",
      width: 100,
      valueType: "digit",
    },
    {
      title: "本期入库",
      dataIndex: "received",
      key: "received",
      width: 100,
      valueType: "digit",
    },
    {
      title: "本期出库",
      dataIndex: "issued",
      key: "issued",
      width: 100,
      valueType: "digit",
    },
    {
      title: "本期退库",
      dataIndex: "returned",
      key: "returned",
      width: 100,
      valueType: "digit",
    },
    {
      title: "期末库存",
      dataIndex: "currentStock",
      key: "currentStock",
      width: 100,
      valueType: "digit",
      render: (_, record) => {
        const isLow = record.currentStock < 100;
        return (
          <span style={{ color: isLow ? "#ff4d4f" : "#52c41a" }}>
            {record.currentStock}
          </span>
        );
      },
    },
    {
      title: "报表日期",
      dataIndex: "reportDate",
      key: "reportDate",
      width: 120,
      valueType: "date",
    },
  ];

  // 开票报表列配置
  const issuingColumns: ProColumns<IssuingReport>[] = [
    {
      title: "部门",
      dataIndex: "department",
      key: "department",
      width: 120,
    },
    {
      title: "票据类型",
      dataIndex: "invoiceType",
      key: "invoiceType",
      width: 150,
    },
    {
      title: "开票数量",
      dataIndex: "issueCount",
      key: "issueCount",
      width: 100,
      valueType: "digit",
    },
    {
      title: "开票金额",
      dataIndex: "issueAmount",
      key: "issueAmount",
      width: 120,
      valueType: "money",
    },
    {
      title: "冲红数量",
      dataIndex: "reverseCount",
      key: "reverseCount",
      width: 100,
      valueType: "digit",
    },
    {
      title: "冲红金额",
      dataIndex: "reverseAmount",
      key: "reverseAmount",
      width: 120,
      valueType: "money",
    },
    {
      title: "净开票数量",
      dataIndex: "netCount",
      key: "netCount",
      width: 120,
      valueType: "digit",
    },
    {
      title: "净开票金额",
      dataIndex: "netAmount",
      key: "netAmount",
      width: 120,
      valueType: "money",
    },
    {
      title: "报表日期",
      dataIndex: "reportDate",
      key: "reportDate",
      width: 120,
      valueType: "date",
    },
  ];

  const handleExport = (format: "excel" | "pdf") => {
    message.success(`正在导出${format.toUpperCase()}格式报表...`);
  };

  const handlePrint = () => {
    message.success("正在准备打印...");
  };

  const renderDepartmentChart = () => {
    const config = {
      data: chartData.departmentIssue,
      xField: "department",
      yField: "count",
      label: {
        position: "middle" as const,
        style: {
          fill: "#FFFFFF",
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: false,
        },
      },
      meta: {
        department: {
          alias: "部门",
        },
        count: {
          alias: "开票数量",
        },
      },
    };
    return <Column {...config} />;
  };

  const renderPieChart = () => {
    const config = {
      appendPadding: 10,
      data: chartData.invoiceTypeDistribution,
      angleField: "value",
      colorField: "type",
      radius: 0.8,
      label: {
        type: "outer",
        content: "{name} {percentage}",
      },
      interactions: [
        {
          type: "pie-legend-active",
        },
        {
          type: "element-active",
        },
      ],
    };
    return <Pie {...config} />;
  };

  const renderTrendChart = () => {
    const config = {
      data: chartData.dailyTrend,
      xField: "date",
      yField: "count",
      point: {
        size: 5,
        shape: "diamond",
      },
      label: {
        style: {
          fill: "#aaa",
        },
      },
    };
    return <Line {...config} />;
  };

  const renderSummaryCards = () => {
    const totalIssue = mockIssuingData.reduce(
      (sum, item) => sum + item.netCount,
      0
    );
    const totalAmount = mockIssuingData.reduce(
      (sum, item) => sum + item.netAmount,
      0
    );
    const totalReverse = mockIssuingData.reduce(
      (sum, item) => sum + item.reverseCount,
      0
    );
    const totalStock = mockInventoryData.reduce(
      (sum, item) => sum + item.currentStock,
      0
    );

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总开票数量"
              value={totalIssue}
              valueStyle={{ color: "#1890ff" }}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总开票金额"
              value={totalAmount}
              precision={2}
              valueStyle={{ color: "#52c41a" }}
              prefix="¥"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="冲红数量"
              value={totalReverse}
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="库存总量"
              value={totalStock}
              valueStyle={{ color: "#722ed1" }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  const tabItems = [
    {
      key: "inventory",
      label: "库存情况报表",
      children: (
        <ProTable<InventoryReport>
          columns={inventoryColumns}
          dataSource={mockInventoryData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: "auto",
          }}
          scroll={{ x: "max-content" }}
          toolBarRender={() => [
            <RangePicker
              key="dateRange"
              onChange={setDateRange}
              placeholder={["开始日期", "结束日期"]}
            />,
            <Button
              key="excel"
              icon={<FileExcelOutlined />}
              onClick={() => handleExport("excel")}
            >
              导出Excel
            </Button>,
            <Button
              key="pdf"
              icon={<FilePdfOutlined />}
              onClick={() => handleExport("pdf")}
            >
              导出PDF
            </Button>,
            <Button
              key="print"
              icon={<PrinterOutlined />}
              onClick={handlePrint}
            >
              打印
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "issuing",
      label: "开票情况报表",
      children: (
        <ProTable<IssuingReport>
          columns={issuingColumns}
          dataSource={mockIssuingData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: "auto",
          }}
          scroll={{ x: "max-content" }}
          toolBarRender={() => [
            <RangePicker
              key="dateRange"
              onChange={setDateRange}
              placeholder={["开始日期", "结束日期"]}
            />,
            <Button
              key="excel"
              icon={<FileExcelOutlined />}
              onClick={() => handleExport("excel")}
            >
              导出Excel
            </Button>,
            <Button
              key="pdf"
              icon={<FilePdfOutlined />}
              onClick={() => handleExport("pdf")}
            >
              导出PDF
            </Button>,
            <Button
              key="print"
              icon={<PrinterOutlined />}
              onClick={handlePrint}
            >
              打印
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "charts",
      label: "统计图表",
      children: (
        <>
          {renderSummaryCards()}
          <Row gutter={16}>
            <Col span={12}>
              <Card title="各部门开票情况" extra={<BarChartOutlined />}>
                {renderDepartmentChart()}
              </Card>
            </Col>
            <Col span={12}>
              <Card title="票据类型分布" extra={<PieChartOutlined />}>
                {renderPieChart()}
              </Card>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title="开票趋势分析" extra={<LineChartOutlined />}>
                {renderTrendChart()}
              </Card>
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <PageContainer
      title="综合报表查询"
      content="提供多维度的票据统计分析报表，支持库存情况查询、开票情况查询和图表分析"
    >
      <ProCard>
        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
      </ProCard>
    </PageContainer>
  );
};

export default ComprehensiveReports;
