// 医疗电子票据平台数据类型定义

// 基础信息管理
export interface OrganizationInfo {
  id: string;
  name: string;
  creditCode: string;
  address: string;
  contact: string;
  phone: string;
  type: string;
  status: string;
  createTime: string;
  updateTime?: string;
}

export interface DepartmentInfo {
  id: string;
  code: string;
  name: string;
  type: string;
  parentId?: string;
  description: string;
  status: string;
  createTime: string;
  updateTime?: string;
}

export interface ChargeItemInfo {
  id: string;
  code: string;
  name: string;
  category: string;
  price: number;
  unit: string;
  description: string;
  status: string;
  createTime: string;
  updateTime?: string;
}

// 票据管理
export interface InvoiceApplication {
  id: string;
  applicationNo: string;
  invoiceType: string;
  quantity: number;
  applicant: string;
  applicationDate: string;
  status: string;
  approver?: string;
  approveDate?: string;
  reason?: string;
  createTime: string;
  updateTime?: string;
}

export interface InvoiceDistribution {
  id: string;
  distributionNo: string;
  invoiceType: string;
  quantity: number;
  fromDepartment: string;
  toDepartment: string;
  distributor: string;
  receiver: string;
  distributionDate: string;
  status: string;
  createTime: string;
  updateTime?: string;
}

export interface InvoiceInventory {
  id: string;
  invoiceType: string;
  invoiceCode: string;
  totalQuantity: number;
  usedQuantity: number;
  remainingQuantity: number;
  department: string;
  lastUpdateTime: string;
  warningThreshold: number;
  status: string;
}

// 票据开具
export interface MedicalInvoice {
  id: string;
  invoiceNo: string;
  invoiceCode: string;
  patientName: string;
  patientId: string;
  visitNo: string;
  department: string;
  doctor: string;
  totalAmount: number;
  issueDate: string;
  status: string;
  paymentMethod: string;
  items: InvoiceItem[];
  createTime: string;
  updateTime?: string;
}

export interface InvoiceItem {
  id: string;
  itemCode: string;
  itemName: string;
  category: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

export interface NonTaxInvoice {
  id: string;
  invoiceNo: string;
  invoiceType: string;
  payerName: string;
  payerIdCard: string;
  amount: number;
  purpose: string;
  issueDate: string;
  status: string;
  operator: string;
  createTime: string;
  updateTime?: string;
}

// 票据存档
export interface ArchivedInvoice {
  id: string;
  invoiceNo: string;
  invoiceType: string;
  patientName: string;
  amount: number;
  issueDate: string;
  archiveDate: string;
  fileFormat: string;
  fileSize: number;
  downloadCount: number;
  status: string;
  filePath: string;
  checksum: string;
  createTime: string;
  updateTime?: string;
}

export interface ArchiveTask {
  id: string;
  taskNo: string;
  taskType: string;
  startDate: string;
  endDate: string;
  totalCount: number;
  successCount: number;
  failCount: number;
  status: string;
  createTime: string;
  completeTime?: string;
  operator: string;
  updateTime?: string;
}

// 票据交付
export interface DeliveryRecord {
  id: string;
  invoiceNo: string;
  patientName: string;
  patientPhone: string;
  deliveryChannel: string;
  deliveryTime: string;
  deliveryStatus: string;
  retryCount: number;
  errorMessage?: string;
  operator: string;
  createTime: string;
  updateTime?: string;
}

export interface DeliveryConfig {
  id: string;
  channel: string;
  enabled: boolean;
  config: any;
  lastUpdateTime: string;
  operator: string;
  createTime: string;
  updateTime?: string;
}

// 综合报表
export interface InventoryReport {
  id: string;
  invoiceType: string;
  department: string;
  initialStock: number;
  received: number;
  issued: number;
  returned: number;
  currentStock: number;
  reportDate: string;
  createTime: string;
}

export interface IssuingReport {
  id: string;
  department: string;
  invoiceType: string;
  issueCount: number;
  issueAmount: number;
  reverseCount: number;
  reverseAmount: number;
  netCount: number;
  netAmount: number;
  reportDate: string;
  createTime: string;
}

// 系统管理
export interface User {
  id: string;
  username: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  roles: string[];
  status: string;
  lastLoginTime: string;
  createTime: string;
  updateTime?: string;
}

export interface Role {
  id: string;
  roleName: string;
  roleCode: string;
  description: string;
  permissions: string[];
  userCount: number;
  status: string;
  createTime: string;
  updateTime?: string;
}

export interface Application {
  id: string;
  appName: string;
  appCode: string;
  appType: string;
  apiKey: string;
  secretKey: string;
  status: string;
  lastAccessTime: string;
  createTime: string;
  description: string;
  updateTime?: string;
}

// 取票小程序
export interface MiniProgramConfig {
  id: string;
  name: string;
  description: string;
  qrCodeUrl: string;
  accessUrl: string;
  logo: string;
  primaryColor: string;
  backgroundColor: string;
  welcomeText: string;
  contactInfo: string;
  status: string;
  createTime: string;
  updateTime: string;
}

export interface AccessLog {
  id: string;
  patientName: string;
  patientPhone: string;
  accessTime: string;
  accessType: string;
  invoiceCount: number;
  userAgent: string;
  ipAddress: string;
  createTime: string;
}

// 通用响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

export interface PaginationParams {
  current?: number;
  pageSize?: number;
  total?: number;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  total: number;
  current: number;
  pageSize: number;
  message?: string;
}

// 查询参数类型
export interface QueryParams {
  [key: string]: any;
  current?: number;
  pageSize?: number;
  sorter?: Record<string, any>;
  filter?: Record<string, any>;
}
