// 医疗电子票据平台API服务
import { message } from 'antd';
import {
  organizationManager,
  departmentManager,
  chargeItemManager,
  invoiceApplicationManager,
  invoiceDistributionManager,
  invoiceInventoryManager,
  medicalInvoiceManager,
  nonTaxInvoiceManager,
  archivedInvoiceManager,
  archiveTaskManager,
  deliveryRecordManager,
  deliveryConfigManager,
  inventoryReportManager,
  issuingReportManager,
  userManager,
  roleManager,
  applicationManager,
  miniProgramConfigManager,
  accessLogManager,
} from './storage';

import type {
  ApiResponse,
  PaginatedResponse,
  QueryParams,
  OrganizationInfo,
  DepartmentInfo,
  ChargeItemInfo,
  InvoiceApplication,
  InvoiceDistribution,
  InvoiceInventory,
  MedicalInvoice,
  NonTaxInvoice,
  ArchivedInvoice,
  ArchiveTask,
  DeliveryRecord,
  DeliveryConfig,
  InventoryReport,
  IssuingReport,
  User,
  Role,
  Application,
  MiniProgramConfig,
  AccessLog,
} from './types';

/**
 * 模拟API延迟
 */
const delay = (ms: number = 300) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 基础信息管理API
 */
export const basicInfoApi = {
  // 机构信息
  async getOrganizations(params?: QueryParams): Promise<PaginatedResponse<OrganizationInfo>> {
    await delay();
    const result = organizationManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createOrganization(data: Omit<OrganizationInfo, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<OrganizationInfo>> {
    await delay();
    const result = organizationManager.add(data);
    return {
      success: true,
      data: result,
      message: '机构信息创建成功',
    };
  },

  async updateOrganization(id: string, data: Partial<OrganizationInfo>): Promise<ApiResponse<OrganizationInfo>> {
    await delay();
    const result = organizationManager.update(id, data);
    if (result) {
      return {
        success: true,
        data: result,
        message: '机构信息更新成功',
      };
    }
    return {
      success: false,
      data: {} as OrganizationInfo,
      message: '机构信息不存在',
    };
  },

  async deleteOrganization(id: string): Promise<ApiResponse<boolean>> {
    await delay();
    const result = organizationManager.delete(id);
    return {
      success: result,
      data: result,
      message: result ? '机构信息删除成功' : '机构信息不存在',
    };
  },

  // 科室信息
  async getDepartments(params?: QueryParams): Promise<PaginatedResponse<DepartmentInfo>> {
    await delay();
    const result = departmentManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createDepartment(data: Omit<DepartmentInfo, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<DepartmentInfo>> {
    await delay();
    const result = departmentManager.add(data);
    return {
      success: true,
      data: result,
      message: '科室信息创建成功',
    };
  },

  async updateDepartment(id: string, data: Partial<DepartmentInfo>): Promise<ApiResponse<DepartmentInfo>> {
    await delay();
    const result = departmentManager.update(id, data);
    if (result) {
      return {
        success: true,
        data: result,
        message: '科室信息更新成功',
      };
    }
    return {
      success: false,
      data: {} as DepartmentInfo,
      message: '科室信息不存在',
    };
  },

  async deleteDepartment(id: string): Promise<ApiResponse<boolean>> {
    await delay();
    const result = departmentManager.delete(id);
    return {
      success: result,
      data: result,
      message: result ? '科室信息删除成功' : '科室信息不存在',
    };
  },

  // 收费项目
  async getChargeItems(params?: QueryParams): Promise<PaginatedResponse<ChargeItemInfo>> {
    await delay();
    const result = chargeItemManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createChargeItem(data: Omit<ChargeItemInfo, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<ChargeItemInfo>> {
    await delay();
    const result = chargeItemManager.add(data);
    return {
      success: true,
      data: result,
      message: '收费项目创建成功',
    };
  },

  async updateChargeItem(id: string, data: Partial<ChargeItemInfo>): Promise<ApiResponse<ChargeItemInfo>> {
    await delay();
    const result = chargeItemManager.update(id, data);
    if (result) {
      return {
        success: true,
        data: result,
        message: '收费项目更新成功',
      };
    }
    return {
      success: false,
      data: {} as ChargeItemInfo,
      message: '收费项目不存在',
    };
  },

  async deleteChargeItem(id: string): Promise<ApiResponse<boolean>> {
    await delay();
    const result = chargeItemManager.delete(id);
    return {
      success: result,
      data: result,
      message: result ? '收费项目删除成功' : '收费项目不存在',
    };
  },
};

/**
 * 票据管理API
 */
export const invoiceManagementApi = {
  // 票据申领
  async getApplications(params?: QueryParams): Promise<PaginatedResponse<InvoiceApplication>> {
    await delay();
    const result = invoiceApplicationManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createApplication(data: Omit<InvoiceApplication, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<InvoiceApplication>> {
    await delay();
    const result = invoiceApplicationManager.add(data);
    return {
      success: true,
      data: result,
      message: '票据申领创建成功',
    };
  },

  async approveApplication(id: string, approver: string): Promise<ApiResponse<InvoiceApplication>> {
    await delay();
    const result = invoiceApplicationManager.update(id, {
      status: '已批准',
      approver,
      approveDate: new Date().toISOString(),
    });
    if (result) {
      return {
        success: true,
        data: result,
        message: '票据申领批准成功',
      };
    }
    return {
      success: false,
      data: {} as InvoiceApplication,
      message: '票据申领不存在',
    };
  },

  async rejectApplication(id: string, reason: string): Promise<ApiResponse<InvoiceApplication>> {
    await delay();
    const result = invoiceApplicationManager.update(id, {
      status: '已拒绝',
      reason,
    });
    if (result) {
      return {
        success: true,
        data: result,
        message: '票据申领已拒绝',
      };
    }
    return {
      success: false,
      data: {} as InvoiceApplication,
      message: '票据申领不存在',
    };
  },

  // 票据分发
  async getDistributions(params?: QueryParams): Promise<PaginatedResponse<InvoiceDistribution>> {
    await delay();
    const result = invoiceDistributionManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createDistribution(data: Omit<InvoiceDistribution, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<InvoiceDistribution>> {
    await delay();
    const result = invoiceDistributionManager.add(data);
    return {
      success: true,
      data: result,
      message: '票据分发创建成功',
    };
  },

  // 票据库存
  async getInventories(params?: QueryParams): Promise<PaginatedResponse<InvoiceInventory>> {
    await delay();
    const result = invoiceInventoryManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async updateInventory(id: string, data: Partial<InvoiceInventory>): Promise<ApiResponse<InvoiceInventory>> {
    await delay();
    const result = invoiceInventoryManager.update(id, data);
    if (result) {
      return {
        success: true,
        data: result,
        message: '库存信息更新成功',
      };
    }
    return {
      success: false,
      data: {} as InvoiceInventory,
      message: '库存信息不存在',
    };
  },
};

/**
 * 票据开具API
 */
export const invoiceIssuingApi = {
  // 医疗票据
  async getMedicalInvoices(params?: QueryParams): Promise<PaginatedResponse<MedicalInvoice>> {
    await delay();
    const result = medicalInvoiceManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createMedicalInvoice(data: Omit<MedicalInvoice, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<MedicalInvoice>> {
    await delay();
    const result = medicalInvoiceManager.add(data);
    return {
      success: true,
      data: result,
      message: '医疗票据开具成功',
    };
  },

  async reverseMedicalInvoice(id: string, reason: string): Promise<ApiResponse<MedicalInvoice>> {
    await delay();
    const result = medicalInvoiceManager.update(id, {
      status: '已冲红',
    });
    if (result) {
      return {
        success: true,
        data: result,
        message: '票据冲红成功',
      };
    }
    return {
      success: false,
      data: {} as MedicalInvoice,
      message: '票据不存在',
    };
  },

  // 非税票据
  async getNonTaxInvoices(params?: QueryParams): Promise<PaginatedResponse<NonTaxInvoice>> {
    await delay();
    const result = nonTaxInvoiceManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createNonTaxInvoice(data: Omit<NonTaxInvoice, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<NonTaxInvoice>> {
    await delay();
    const result = nonTaxInvoiceManager.add(data);
    return {
      success: true,
      data: result,
      message: '非税票据开具成功',
    };
  },
};

/**
 * 票据存档API
 */
export const invoiceArchiveApi = {
  // 已存档票据
  async getArchivedInvoices(params?: QueryParams): Promise<PaginatedResponse<ArchivedInvoice>> {
    await delay();
    const result = archivedInvoiceManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async downloadInvoice(id: string): Promise<ApiResponse<string>> {
    await delay();
    const invoice = archivedInvoiceManager.getById(id);
    if (invoice) {
      // 增加下载次数
      archivedInvoiceManager.update(id, {
        downloadCount: invoice.downloadCount + 1,
      });
      return {
        success: true,
        data: invoice.filePath,
        message: '文件下载成功',
      };
    }
    return {
      success: false,
      data: '',
      message: '文件不存在',
    };
  },

  // 存档任务
  async getArchiveTasks(params?: QueryParams): Promise<PaginatedResponse<ArchiveTask>> {
    await delay();
    const result = archiveTaskManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createArchiveTask(data: Omit<ArchiveTask, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<ArchiveTask>> {
    await delay();
    const result = archiveTaskManager.add(data);
    return {
      success: true,
      data: result,
      message: '存档任务创建成功',
    };
  },
};

/**
 * 票据交付API
 */
export const invoiceDeliveryApi = {
  // 交付记录
  async getDeliveryRecords(params?: QueryParams): Promise<PaginatedResponse<DeliveryRecord>> {
    await delay();
    const result = deliveryRecordManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async retryDelivery(id: string): Promise<ApiResponse<DeliveryRecord>> {
    await delay();
    const record = deliveryRecordManager.getById(id);
    if (record) {
      const result = deliveryRecordManager.update(id, {
        retryCount: record.retryCount + 1,
        deliveryStatus: '处理中',
      });
      return {
        success: true,
        data: result!,
        message: '重试任务已启动',
      };
    }
    return {
      success: false,
      data: {} as DeliveryRecord,
      message: '交付记录不存在',
    };
  },

  // 交付配置
  async getDeliveryConfigs(): Promise<ApiResponse<DeliveryConfig[]>> {
    await delay();
    const result = deliveryConfigManager.getAll();
    return {
      success: true,
      data: result,
    };
  },

  async updateDeliveryConfig(id: string, data: Partial<DeliveryConfig>): Promise<ApiResponse<DeliveryConfig>> {
    await delay();
    const result = deliveryConfigManager.update(id, data);
    if (result) {
      return {
        success: true,
        data: result,
        message: '配置更新成功',
      };
    }
    return {
      success: false,
      data: {} as DeliveryConfig,
      message: '配置不存在',
    };
  },
};

/**
 * 综合报表API
 */
export const reportsApi = {
  // 库存报表
  async getInventoryReports(params?: QueryParams): Promise<PaginatedResponse<InventoryReport>> {
    await delay();
    const result = inventoryReportManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  // 开票报表
  async getIssuingReports(params?: QueryParams): Promise<PaginatedResponse<IssuingReport>> {
    await delay();
    const result = issuingReportManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async exportReport(type: string, format: string): Promise<ApiResponse<string>> {
    await delay();
    return {
      success: true,
      data: `report_${type}_${Date.now()}.${format}`,
      message: '报表导出成功',
    };
  },
};

/**
 * 系统管理API
 */
export const systemManagementApi = {
  // 用户管理
  async getUsers(params?: QueryParams): Promise<PaginatedResponse<User>> {
    await delay();
    const result = userManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createUser(data: Omit<User, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<User>> {
    await delay();
    const result = userManager.add(data);
    return {
      success: true,
      data: result,
      message: '用户创建成功',
    };
  },

  async updateUser(id: string, data: Partial<User>): Promise<ApiResponse<User>> {
    await delay();
    const result = userManager.update(id, data);
    if (result) {
      return {
        success: true,
        data: result,
        message: '用户信息更新成功',
      };
    }
    return {
      success: false,
      data: {} as User,
      message: '用户不存在',
    };
  },

  async deleteUser(id: string): Promise<ApiResponse<boolean>> {
    await delay();
    const result = userManager.delete(id);
    return {
      success: result,
      data: result,
      message: result ? '用户删除成功' : '用户不存在',
    };
  },

  async resetPassword(id: string): Promise<ApiResponse<boolean>> {
    await delay();
    return {
      success: true,
      data: true,
      message: '密码重置成功，新密码已发送到用户邮箱',
    };
  },

  // 角色管理
  async getRoles(params?: QueryParams): Promise<PaginatedResponse<Role>> {
    await delay();
    const result = roleManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createRole(data: Omit<Role, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<Role>> {
    await delay();
    const result = roleManager.add(data);
    return {
      success: true,
      data: result,
      message: '角色创建成功',
    };
  },

  async updateRole(id: string, data: Partial<Role>): Promise<ApiResponse<Role>> {
    await delay();
    const result = roleManager.update(id, data);
    if (result) {
      return {
        success: true,
        data: result,
        message: '角色信息更新成功',
      };
    }
    return {
      success: false,
      data: {} as Role,
      message: '角色不存在',
    };
  },

  async deleteRole(id: string): Promise<ApiResponse<boolean>> {
    await delay();
    const result = roleManager.delete(id);
    return {
      success: result,
      data: result,
      message: result ? '角色删除成功' : '角色不存在',
    };
  },

  // 应用管理
  async getApplications(params?: QueryParams): Promise<PaginatedResponse<Application>> {
    await delay();
    const result = applicationManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },

  async createApplication(data: Omit<Application, 'id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<Application>> {
    await delay();
    const result = applicationManager.add(data);
    return {
      success: true,
      data: result,
      message: '应用创建成功',
    };
  },

  async regenerateApiKey(id: string): Promise<ApiResponse<Application>> {
    await delay();
    const newApiKey = `ak_${Date.now()}`;
    const newSecretKey = `sk_${Date.now()}`;
    const result = applicationManager.update(id, {
      apiKey: newApiKey,
      secretKey: newSecretKey,
    });
    if (result) {
      return {
        success: true,
        data: result,
        message: 'API密钥重新生成成功',
      };
    }
    return {
      success: false,
      data: {} as Application,
      message: '应用不存在',
    };
  },
};

/**
 * 取票小程序API
 */
export const miniProgramApi = {
  // 小程序配置
  async getConfigs(): Promise<ApiResponse<MiniProgramConfig[]>> {
    await delay();
    const result = miniProgramConfigManager.getAll();
    return {
      success: true,
      data: result,
    };
  },

  async createConfig(data: Omit<MiniProgramConfig, 'id' | 'createTime'>): Promise<ApiResponse<MiniProgramConfig>> {
    await delay();
    const result = miniProgramConfigManager.add(data);
    return {
      success: true,
      data: result,
      message: '小程序配置创建成功',
    };
  },

  async updateConfig(id: string, data: Partial<MiniProgramConfig>): Promise<ApiResponse<MiniProgramConfig>> {
    await delay();
    const result = miniProgramConfigManager.update(id, data);
    if (result) {
      return {
        success: true,
        data: result,
        message: '小程序配置更新成功',
      };
    }
    return {
      success: false,
      data: {} as MiniProgramConfig,
      message: '配置不存在',
    };
  },

  // 访问日志
  async getAccessLogs(params?: QueryParams): Promise<PaginatedResponse<AccessLog>> {
    await delay();
    const result = accessLogManager.paginate(params || {});
    return {
      success: true,
      ...result,
    };
  },
};
