import {
  <PERSON><PERSON>ontainer,
  ProCard,
  ProTable,
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
  ProFormDigit,
  ProFormDatePicker,
} from "@ant-design/pro-components";
import { Button, message, Modal, Space, Tabs, Tag, Progress } from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  ImportOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import React, { useState, useRef } from "react";
import type { ActionType, ProColumns } from "@ant-design/pro-components";

// 定义数据类型
interface InvoiceApplication {
  id: string;
  applicationNo: string;
  invoiceType: string;
  quantity: number;
  applicant: string;
  applicationDate: string;
  status: string;
  approver?: string;
  approveDate?: string;
  reason?: string;
}

interface InvoiceDistribution {
  id: string;
  distributionNo: string;
  invoiceType: string;
  quantity: number;
  fromDepartment: string;
  toDepartment: string;
  distributor: string;
  receiver: string;
  distributionDate: string;
  status: string;
}

interface InvoiceInventory {
  id: string;
  invoiceType: string;
  invoiceCode: string;
  totalQuantity: number;
  usedQuantity: number;
  remainingQuantity: number;
  department: string;
  lastUpdateTime: string;
  warningThreshold: number;
  status: string;
}

const InvoiceManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState("application");
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [modalType, setModalType] = useState<"add" | "edit">("add");
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockApplicationData: InvoiceApplication[] = [
    {
      id: "1",
      applicationNo: "SQ202501001",
      invoiceType: "门诊医疗电子票据",
      quantity: 1000,
      applicant: "张三",
      applicationDate: "2025-01-01 09:00:00",
      status: "待审批",
    },
    {
      id: "2",
      applicationNo: "SQ202501002",
      invoiceType: "住院医疗电子票据",
      quantity: 500,
      applicant: "李四",
      applicationDate: "2025-01-02 10:00:00",
      status: "已批准",
      approver: "王五",
      approveDate: "2025-01-02 14:00:00",
    },
  ];

  const mockDistributionData: InvoiceDistribution[] = [
    {
      id: "1",
      distributionNo: "FP202501001",
      invoiceType: "门诊医疗电子票据",
      quantity: 200,
      fromDepartment: "财务科",
      toDepartment: "门诊部",
      distributor: "张三",
      receiver: "李四",
      distributionDate: "2025-01-03 09:00:00",
      status: "已分发",
    },
  ];

  const mockInventoryData: InvoiceInventory[] = [
    {
      id: "1",
      invoiceType: "门诊医疗电子票据",
      invoiceCode: "MZPJ001",
      totalQuantity: 1000,
      usedQuantity: 150,
      remainingQuantity: 850,
      department: "门诊部",
      lastUpdateTime: "2025-01-10 15:30:00",
      warningThreshold: 100,
      status: "正常",
    },
    {
      id: "2",
      invoiceType: "住院医疗电子票据",
      invoiceCode: "ZYPJ001",
      totalQuantity: 500,
      usedQuantity: 450,
      remainingQuantity: 50,
      department: "住院部",
      lastUpdateTime: "2025-01-10 16:00:00",
      warningThreshold: 100,
      status: "预警",
    },
  ];

  // 票据申领列配置
  const applicationColumns: ProColumns<InvoiceApplication>[] = [
    {
      title: "申领单号",
      dataIndex: "applicationNo",
      key: "applicationNo",
      width: 120,
    },
    {
      title: "票据类型",
      dataIndex: "invoiceType",
      key: "invoiceType",
      width: 150,
    },
    {
      title: "申领数量",
      dataIndex: "quantity",
      key: "quantity",
      width: 100,
      valueType: "digit",
    },
    {
      title: "申请人",
      dataIndex: "applicant",
      key: "applicant",
      width: 100,
    },
    {
      title: "申请时间",
      dataIndex: "applicationDate",
      key: "applicationDate",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          待审批: { color: "orange", text: "待审批" },
          已批准: { color: "green", text: "已批准" },
          已拒绝: { color: "red", text: "已拒绝" },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: "审批人",
      dataIndex: "approver",
      key: "approver",
      width: 100,
    },
    {
      title: "审批时间",
      dataIndex: "approveDate",
      key: "approveDate",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.status === "待审批" && (
            <>
              <Button
                type="link"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleApprove(record.id)}
              >
                批准
              </Button>
              <Button
                type="link"
                size="small"
                danger
                icon={<CloseCircleOutlined />}
                onClick={() => handleReject(record.id)}
              >
                拒绝
              </Button>
            </>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 票据分发列配置
  const distributionColumns: ProColumns<InvoiceDistribution>[] = [
    {
      title: "分发单号",
      dataIndex: "distributionNo",
      key: "distributionNo",
      width: 120,
    },
    {
      title: "票据类型",
      dataIndex: "invoiceType",
      key: "invoiceType",
      width: 150,
    },
    {
      title: "分发数量",
      dataIndex: "quantity",
      key: "quantity",
      width: 100,
      valueType: "digit",
    },
    {
      title: "分发部门",
      dataIndex: "fromDepartment",
      key: "fromDepartment",
      width: 120,
    },
    {
      title: "接收部门",
      dataIndex: "toDepartment",
      key: "toDepartment",
      width: 120,
    },
    {
      title: "分发人",
      dataIndex: "distributor",
      key: "distributor",
      width: 100,
    },
    {
      title: "接收人",
      dataIndex: "receiver",
      key: "receiver",
      width: 100,
    },
    {
      title: "分发时间",
      dataIndex: "distributionDate",
      key: "distributionDate",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          待确认: { color: "orange", text: "待确认" },
          已分发: { color: "green", text: "已分发" },
          已拒收: { color: "red", text: "已拒收" },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 库存管理列配置
  const inventoryColumns: ProColumns<InvoiceInventory>[] = [
    {
      title: "票据类型",
      dataIndex: "invoiceType",
      key: "invoiceType",
      width: 150,
    },
    {
      title: "票据代码",
      dataIndex: "invoiceCode",
      key: "invoiceCode",
      width: 120,
    },
    {
      title: "总数量",
      dataIndex: "totalQuantity",
      key: "totalQuantity",
      width: 100,
      valueType: "digit",
    },
    {
      title: "已使用",
      dataIndex: "usedQuantity",
      key: "usedQuantity",
      width: 100,
      valueType: "digit",
    },
    {
      title: "剩余数量",
      dataIndex: "remainingQuantity",
      key: "remainingQuantity",
      width: 100,
      valueType: "digit",
      render: (_, record) => {
        const isWarning = record.remainingQuantity <= record.warningThreshold;
        return (
          <span style={{ color: isWarning ? "#ff4d4f" : "#52c41a" }}>
            {record.remainingQuantity}
          </span>
        );
      },
    },
    {
      title: "使用率",
      key: "usageRate",
      width: 120,
      render: (_, record) => {
        const rate = (record.usedQuantity / record.totalQuantity) * 100;
        return (
          <Progress
            percent={Number(rate.toFixed(1))}
            size="small"
            status={rate > 90 ? "exception" : rate > 70 ? "active" : "success"}
          />
        );
      },
    },
    {
      title: "所属部门",
      dataIndex: "department",
      key: "department",
      width: 120,
    },
    {
      title: "最后更新",
      dataIndex: "lastUpdateTime",
      key: "lastUpdateTime",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          正常: { color: "green", text: "正常" },
          预警: { color: "orange", text: "预警" },
          告急: { color: "red", text: "告急" },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            调整
          </Button>
          <Button
            type="link"
            size="small"
            icon={<ExportOutlined />}
            onClick={() => handleExport(record.id)}
          >
            导出
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setModalType("add");
    setEditingRecord(null);
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setModalType("edit");
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这条记录吗？",
      onOk: () => {
        message.success("删除成功");
        actionRef.current?.reload();
      },
    });
  };

  const handleApprove = (id: string) => {
    Modal.confirm({
      title: "确认批准",
      content: "确定要批准这个申请吗？",
      onOk: () => {
        message.success("批准成功");
        actionRef.current?.reload();
      },
    });
  };

  const handleReject = (id: string) => {
    Modal.confirm({
      title: "确认拒绝",
      content: "确定要拒绝这个申请吗？",
      onOk: () => {
        message.success("已拒绝申请");
        actionRef.current?.reload();
      },
    });
  };

  const handleExport = (id: string) => {
    message.success("导出成功");
  };

  const handleModalOk = async (values: any) => {
    try {
      if (modalType === "add") {
        message.success("新增成功");
      } else {
        message.success("编辑成功");
      }
      setModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error("操作失败");
    }
  };

  const tabItems = [
    {
      key: "application",
      label: "票据申领",
      children: (
        <ProTable<InvoiceApplication>
          columns={applicationColumns}
          dataSource={mockApplicationData}
          rowKey="id"
          actionRef={actionRef}
          search={false}
          scroll={{ x: "max-content" }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增申领
            </Button>,
            <Button key="import" icon={<ImportOutlined />}>
              批量导入
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "distribution",
      label: "票据分发",
      children: (
        <ProTable<InvoiceDistribution>
          columns={distributionColumns}
          dataSource={mockDistributionData}
          rowKey="id"
          actionRef={actionRef}
          search={false}
          scroll={{ x: "max-content" }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增分发
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "inventory",
      label: "库存管理",
      children: (
        <ProTable<InvoiceInventory>
          columns={inventoryColumns}
          dataSource={mockInventoryData}
          rowKey="id"
          actionRef={actionRef}
          search={false}
          scroll={{ x: "max-content" }}
          toolBarRender={() => [
            <Button key="refresh" onClick={() => actionRef.current?.reload()}>
              刷新库存
            </Button>,
            <Button
              key="export"
              icon={<ExportOutlined />}
              onClick={() => handleExport("all")}
            >
              导出报表
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
  ];

  const renderModalForm = () => {
    const getModalTitle = () => {
      const typeMap = {
        application: "票据申领",
        distribution: "票据分发",
        inventory: "库存调整",
      };
      const typeName = typeMap[activeTab as keyof typeof typeMap] || "信息";
      return modalType === "add" ? `新增${typeName}` : `编辑${typeName}`;
    };

    const commonModalProps = {
      title: getModalTitle(),
      open: modalVisible,
      onCancel: () => setModalVisible(false),
      width: 600,
      destroyOnClose: true,
      footer: null,
    };

    if (activeTab === "application") {
      return (
        <Modal {...commonModalProps}>
          <ProForm
            initialValues={editingRecord}
            onFinish={handleModalOk}
            submitter={{
              searchConfig: {
                submitText: modalType === "add" ? "新增" : "保存",
                resetText: "取消",
              },
              resetButtonProps: {
                onClick: () => setModalVisible(false),
              },
            }}
          >
            <ProFormText
              name="applicationNo"
              label="申领单号"
              rules={[
                { required: true, message: "请输入申领单号" },
                { pattern: /^SQ\d{9}$/, message: "申领单号格式：SQ + 9位数字" },
              ]}
              placeholder="请输入申领单号，如：SQ202501001"
              disabled={modalType === "edit"}
            />
            <ProFormSelect
              name="invoiceType"
              label="票据类型"
              options={[
                { label: "门诊医疗电子票据", value: "门诊医疗电子票据" },
                { label: "住院医疗电子票据", value: "住院医疗电子票据" },
                { label: "非税收入票据", value: "非税收入票据" },
                { label: "往来结算票据", value: "往来结算票据" },
              ]}
              rules={[{ required: true, message: "请选择票据类型" }]}
              placeholder="请选择票据类型"
            />
            <ProFormDigit
              name="quantity"
              label="申领数量"
              rules={[
                { required: true, message: "请输入申领数量" },
                {
                  type: "number",
                  min: 1,
                  max: 10000,
                  message: "申领数量应在1-10000之间",
                },
              ]}
              placeholder="请输入申领数量"
              fieldProps={{
                min: 1,
                max: 10000,
                precision: 0,
                addonAfter: "张",
              }}
            />
            <ProFormText
              name="applicant"
              label="申请人"
              rules={[
                { required: true, message: "请输入申请人" },
                { max: 20, message: "申请人姓名不能超过20个字符" },
              ]}
              placeholder="请输入申请人姓名"
            />
            <ProFormDatePicker
              name="applicationDate"
              label="申请时间"
              rules={[{ required: true, message: "请选择申请时间" }]}
              fieldProps={{
                showTime: true,
                format: "YYYY-MM-DD HH:mm:ss",
              }}
            />
            <ProFormTextArea
              name="reason"
              label="申请理由"
              rules={[{ max: 200, message: "申请理由不能超过200个字符" }]}
              placeholder="请输入申请理由（可选）"
              fieldProps={{ rows: 3 }}
            />
          </ProForm>
        </Modal>
      );
    } else if (activeTab === "distribution") {
      return (
        <Modal {...commonModalProps}>
          <ProForm
            initialValues={editingRecord}
            onFinish={handleModalOk}
            submitter={{
              searchConfig: {
                submitText: modalType === "add" ? "新增" : "保存",
                resetText: "取消",
              },
              resetButtonProps: {
                onClick: () => setModalVisible(false),
              },
            }}
          >
            <ProFormText
              name="distributionNo"
              label="分发单号"
              rules={[
                { required: true, message: "请输入分发单号" },
                { pattern: /^FP\d{9}$/, message: "分发单号格式：FP + 9位数字" },
              ]}
              placeholder="请输入分发单号，如：FP202501001"
              disabled={modalType === "edit"}
            />
            <ProFormSelect
              name="invoiceType"
              label="票据类型"
              options={[
                { label: "门诊医疗电子票据", value: "门诊医疗电子票据" },
                { label: "住院医疗电子票据", value: "住院医疗电子票据" },
                { label: "非税收入票据", value: "非税收入票据" },
                { label: "往来结算票据", value: "往来结算票据" },
              ]}
              rules={[{ required: true, message: "请选择票据类型" }]}
              placeholder="请选择票据类型"
            />
            <ProFormDigit
              name="quantity"
              label="分发数量"
              rules={[
                { required: true, message: "请输入分发数量" },
                {
                  type: "number",
                  min: 1,
                  max: 10000,
                  message: "分发数量应在1-10000之间",
                },
              ]}
              placeholder="请输入分发数量"
              fieldProps={{
                min: 1,
                max: 10000,
                precision: 0,
                addonAfter: "张",
              }}
            />
            <ProFormSelect
              name="fromDepartment"
              label="分发部门"
              options={[
                { label: "财务科", value: "财务科" },
                { label: "信息科", value: "信息科" },
                { label: "总务科", value: "总务科" },
              ]}
              rules={[{ required: true, message: "请选择分发部门" }]}
              placeholder="请选择分发部门"
            />
            <ProFormSelect
              name="toDepartment"
              label="接收部门"
              options={[
                { label: "门诊部", value: "门诊部" },
                { label: "住院部", value: "住院部" },
                { label: "急诊科", value: "急诊科" },
                { label: "体检中心", value: "体检中心" },
              ]}
              rules={[{ required: true, message: "请选择接收部门" }]}
              placeholder="请选择接收部门"
            />
            <ProFormText
              name="distributor"
              label="分发人"
              rules={[
                { required: true, message: "请输入分发人" },
                { max: 20, message: "分发人姓名不能超过20个字符" },
              ]}
              placeholder="请输入分发人姓名"
            />
            <ProFormText
              name="receiver"
              label="接收人"
              rules={[
                { required: true, message: "请输入接收人" },
                { max: 20, message: "接收人姓名不能超过20个字符" },
              ]}
              placeholder="请输入接收人姓名"
            />
            <ProFormDatePicker
              name="distributionDate"
              label="分发时间"
              rules={[{ required: true, message: "请选择分发时间" }]}
              fieldProps={{
                showTime: true,
                format: "YYYY-MM-DD HH:mm:ss",
              }}
            />
          </ProForm>
        </Modal>
      );
    } else if (activeTab === "inventory") {
      return (
        <Modal {...commonModalProps}>
          <ProForm
            initialValues={editingRecord}
            onFinish={handleModalOk}
            submitter={{
              searchConfig: {
                submitText: "保存",
                resetText: "取消",
              },
              resetButtonProps: {
                onClick: () => setModalVisible(false),
              },
            }}
          >
            <ProFormText
              name="invoiceType"
              label="票据类型"
              disabled
              placeholder="票据类型（不可修改）"
            />
            <ProFormText
              name="invoiceCode"
              label="票据代码"
              disabled
              placeholder="票据代码（不可修改）"
            />
            <ProFormText
              name="department"
              label="所属部门"
              disabled
              placeholder="所属部门（不可修改）"
            />
            <ProFormDigit
              name="totalQuantity"
              label="总数量"
              rules={[
                { required: true, message: "请输入总数量" },
                { type: "number", min: 0, message: "总数量不能为负数" },
              ]}
              placeholder="请输入总数量"
              fieldProps={{
                min: 0,
                precision: 0,
                addonAfter: "张",
              }}
            />
            <ProFormDigit
              name="usedQuantity"
              label="已使用数量"
              rules={[
                { required: true, message: "请输入已使用数量" },
                { type: "number", min: 0, message: "已使用数量不能为负数" },
              ]}
              placeholder="请输入已使用数量"
              fieldProps={{
                min: 0,
                precision: 0,
                addonAfter: "张",
              }}
            />
            <ProFormDigit
              name="warningThreshold"
              label="预警阈值"
              rules={[
                { required: true, message: "请输入预警阈值" },
                { type: "number", min: 0, message: "预警阈值不能为负数" },
              ]}
              placeholder="请输入预警阈值"
              fieldProps={{
                min: 0,
                precision: 0,
                addonAfter: "张",
              }}
            />
          </ProForm>
        </Modal>
      );
    }

    return null;
  };

  return (
    <PageContainer
      title="票据管理"
      content="管理票据的申领、分发、申退、审验、销毁和库存等全生命周期流程"
    >
      <ProCard>
        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
      </ProCard>
      {renderModalForm()}
    </PageContainer>
  );
};

export default InvoiceManagement;
