// Mock数据初始化服务
import {
  organizationManager,
  departmentManager,
  chargeItemManager,
  invoiceApplicationManager,
  invoiceDistributionManager,
  invoiceInventoryManager,
  medicalInvoiceManager,
  nonTaxInvoiceManager,
  archivedInvoiceManager,
  archiveTaskManager,
  deliveryRecordManager,
  deliveryConfigManager,
  inventoryReportManager,
  issuingReportManager,
  userManager,
  roleManager,
  applicationManager,
  miniProgramConfigManager,
  accessLogManager,
} from './storage';

import type {
  OrganizationInfo,
  DepartmentInfo,
  ChargeItemInfo,
  InvoiceApplication,
  InvoiceDistribution,
  InvoiceInventory,
  MedicalInvoice,
  NonTaxInvoice,
  ArchivedInvoice,
  ArchiveTask,
  DeliveryRecord,
  DeliveryConfig,
  InventoryReport,
  IssuingReport,
  User,
  Role,
  Application,
  MiniProgramConfig,
  AccessLog,
} from './types';

/**
 * Mock数据初始化类
 */
export class MockDataInitializer {
  /**
   * 初始化所有Mock数据
   */
  static initializeAll(): void {
    console.log('开始初始化Mock数据...');
    
    // 检查是否已经初始化过
    if (this.isInitialized()) {
      console.log('Mock数据已存在，跳过初始化');
      return;
    }

    try {
      this.initializeBasicInfo();
      this.initializeInvoiceManagement();
      this.initializeInvoiceIssuing();
      this.initializeInvoiceArchive();
      this.initializeInvoiceDelivery();
      this.initializeReports();
      this.initializeSystemManagement();
      this.initializeMiniProgram();
      
      // 标记已初始化
      localStorage.setItem('medical_invoice_mock_initialized', 'true');
      console.log('Mock数据初始化完成');
    } catch (error) {
      console.error('Mock数据初始化失败:', error);
    }
  }

  /**
   * 检查是否已初始化
   */
  private static isInitialized(): boolean {
    return localStorage.getItem('medical_invoice_mock_initialized') === 'true';
  }

  /**
   * 重置所有数据
   */
  static resetAll(): void {
    localStorage.removeItem('medical_invoice_mock_initialized');
    organizationManager.clear();
    departmentManager.clear();
    chargeItemManager.clear();
    invoiceApplicationManager.clear();
    invoiceDistributionManager.clear();
    invoiceInventoryManager.clear();
    medicalInvoiceManager.clear();
    nonTaxInvoiceManager.clear();
    archivedInvoiceManager.clear();
    archiveTaskManager.clear();
    deliveryRecordManager.clear();
    deliveryConfigManager.clear();
    inventoryReportManager.clear();
    issuingReportManager.clear();
    userManager.clear();
    roleManager.clear();
    applicationManager.clear();
    miniProgramConfigManager.clear();
    accessLogManager.clear();
    
    console.log('所有Mock数据已重置');
  }

  /**
   * 初始化基础信息数据
   */
  private static initializeBasicInfo(): void {
    // 机构信息
    const organizations: Omit<OrganizationInfo, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        name: '北京市第一人民医院',
        creditCode: '12110000400000123X',
        address: '北京市朝阳区建国路1号',
        contact: '张三',
        phone: '010-12345678',
        type: '三甲医院',
        status: '正常',
      },
      {
        name: '北京市第二人民医院',
        creditCode: '12110000400000124X',
        address: '北京市海淀区中关村大街2号',
        contact: '李四',
        phone: '010-87654321',
        type: '三甲医院',
        status: '正常',
      },
    ];

    // 科室信息
    const departments: Omit<DepartmentInfo, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        code: 'NEI01',
        name: '内科',
        type: '临床科室',
        description: '内科诊疗科室',
        status: '正常',
      },
      {
        code: 'WAI01',
        name: '外科',
        type: '临床科室',
        description: '外科诊疗科室',
        status: '正常',
      },
      {
        code: 'FUK01',
        name: '妇科',
        type: '临床科室',
        description: '妇科诊疗科室',
        status: '正常',
      },
      {
        code: 'ERK01',
        name: '儿科',
        type: '临床科室',
        description: '儿科诊疗科室',
        status: '正常',
      },
      {
        code: 'YXK01',
        name: '影像科',
        type: '医技科室',
        description: '医学影像科室',
        status: '正常',
      },
    ];

    // 收费项目
    const chargeItems: Omit<ChargeItemInfo, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        code: 'GHFEI001',
        name: '普通门诊挂号费',
        category: '挂号费',
        price: 5.00,
        unit: '次',
        description: '普通门诊挂号收费项目',
        status: '正常',
      },
      {
        code: 'GHFEI002',
        name: '专家门诊挂号费',
        category: '挂号费',
        price: 15.00,
        unit: '次',
        description: '专家门诊挂号收费项目',
        status: '正常',
      },
      {
        code: 'JCFEI001',
        name: '血常规检查',
        category: '检查费',
        price: 25.00,
        unit: '次',
        description: '血常规化验检查费用',
        status: '正常',
      },
      {
        code: 'JCFEI002',
        name: 'CT检查',
        category: '检查费',
        price: 300.00,
        unit: '次',
        description: 'CT影像检查费用',
        status: '正常',
      },
      {
        code: 'ZLFEI001',
        name: '伤口处理',
        category: '治疗费',
        price: 70.00,
        unit: '次',
        description: '外科伤口处理费用',
        status: '正常',
      },
    ];

    organizations.forEach(org => organizationManager.add(org));
    departments.forEach(dept => departmentManager.add(dept));
    chargeItems.forEach(item => chargeItemManager.add(item));
  }

  /**
   * 初始化票据管理数据
   */
  private static initializeInvoiceManagement(): void {
    // 票据申领
    const applications: Omit<InvoiceApplication, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        applicationNo: 'SQ202501001',
        invoiceType: '门诊医疗电子票据',
        quantity: 1000,
        applicant: '张三',
        applicationDate: '2025-01-01 09:00:00',
        status: '待审批',
      },
      {
        applicationNo: 'SQ202501002',
        invoiceType: '住院医疗电子票据',
        quantity: 500,
        applicant: '李四',
        applicationDate: '2025-01-02 10:00:00',
        status: '已批准',
        approver: '王五',
        approveDate: '2025-01-02 14:00:00',
      },
    ];

    // 票据分发
    const distributions: Omit<InvoiceDistribution, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        distributionNo: 'FP202501001',
        invoiceType: '门诊医疗电子票据',
        quantity: 200,
        fromDepartment: '财务科',
        toDepartment: '门诊部',
        distributor: '张三',
        receiver: '李四',
        distributionDate: '2025-01-03 09:00:00',
        status: '已分发',
      },
    ];

    // 票据库存
    const inventories: Omit<InvoiceInventory, 'id'>[] = [
      {
        invoiceType: '门诊医疗电子票据',
        invoiceCode: 'MZPJ001',
        totalQuantity: 1000,
        usedQuantity: 150,
        remainingQuantity: 850,
        department: '门诊部',
        lastUpdateTime: '2025-01-10 15:30:00',
        warningThreshold: 100,
        status: '正常',
      },
      {
        invoiceType: '住院医疗电子票据',
        invoiceCode: 'ZYPJ001',
        totalQuantity: 500,
        usedQuantity: 450,
        remainingQuantity: 50,
        department: '住院部',
        lastUpdateTime: '2025-01-10 16:00:00',
        warningThreshold: 100,
        status: '预警',
      },
    ];

    applications.forEach(app => invoiceApplicationManager.add(app));
    distributions.forEach(dist => invoiceDistributionManager.add(dist));
    inventories.forEach(inv => invoiceInventoryManager.add(inv));
  }

  /**
   * 初始化票据开具数据
   */
  private static initializeInvoiceIssuing(): void {
    // 医疗票据
    const medicalInvoices: Omit<MedicalInvoice, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        invoiceNo: 'PJ20250110001',
        invoiceCode: 'MZPJ001',
        patientName: '张三',
        patientId: '110101199001011234',
        visitNo: 'MZ20250110001',
        department: '内科',
        doctor: '李医生',
        totalAmount: 125.50,
        issueDate: '2025-01-10 09:30:00',
        status: '正常',
        paymentMethod: '微信支付',
        items: [
          {
            id: '1',
            itemCode: 'GHFEI001',
            itemName: '普通门诊挂号费',
            category: '挂号费',
            quantity: 1,
            unitPrice: 5.00,
            amount: 5.00,
          },
          {
            id: '2',
            itemCode: 'JCFEI001',
            itemName: '血常规检查',
            category: '检查费',
            quantity: 1,
            unitPrice: 25.00,
            amount: 25.00,
          },
        ],
      },
    ];

    // 非税票据
    const nonTaxInvoices: Omit<NonTaxInvoice, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        invoiceNo: 'FS20250110001',
        invoiceType: '非税收入票据',
        payerName: '张三',
        payerIdCard: '110101199001011234',
        amount: 500.00,
        purpose: '体检费',
        issueDate: '2025-01-10 14:00:00',
        status: '正常',
        operator: '财务人员A',
      },
    ];

    medicalInvoices.forEach(invoice => medicalInvoiceManager.add(invoice));
    nonTaxInvoices.forEach(invoice => nonTaxInvoiceManager.add(invoice));
  }

  /**
   * 初始化票据存档数据
   */
  private static initializeInvoiceArchive(): void {
    // 已存档票据
    const archivedInvoices: Omit<ArchivedInvoice, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        invoiceNo: 'PJ20250110001',
        invoiceType: '门诊医疗电子票据',
        patientName: '张三',
        amount: 125.50,
        issueDate: '2025-01-10 09:30:00',
        archiveDate: '2025-01-10 10:00:00',
        fileFormat: 'PDF',
        fileSize: 245760,
        downloadCount: 3,
        status: '已存档',
        filePath: '/archive/2025/01/PJ20250110001.pdf',
        checksum: 'a1b2c3d4e5f6',
      },
    ];

    // 存档任务
    const archiveTasks: Omit<ArchiveTask, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        taskNo: 'TASK20250110001',
        taskType: '定时同步',
        startDate: '2025-01-10 00:00:00',
        endDate: '2025-01-10 23:59:59',
        totalCount: 150,
        successCount: 148,
        failCount: 2,
        status: '已完成',
        completeTime: '2025-01-10 08:30:00',
        operator: '系统自动',
      },
    ];

    archivedInvoices.forEach(invoice => archivedInvoiceManager.add(invoice));
    archiveTasks.forEach(task => archiveTaskManager.add(task));
  }

  /**
   * 初始化票据交付数据
   */
  private static initializeInvoiceDelivery(): void {
    // 交付记录
    const deliveryRecords: Omit<DeliveryRecord, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        invoiceNo: 'PJ20250110001',
        patientName: '张三',
        patientPhone: '13800138001',
        deliveryChannel: '短信',
        deliveryTime: '2025-01-10 09:35:00',
        deliveryStatus: '成功',
        retryCount: 0,
        operator: '系统自动',
      },
      {
        invoiceNo: 'PJ2**********',
        patientName: '李四',
        patientPhone: '13800138002',
        deliveryChannel: 'APP推送',
        deliveryTime: '2025-01-10 10:20:00',
        deliveryStatus: '失败',
        retryCount: 2,
        errorMessage: '用户未安装APP',
        operator: '系统自动',
      },
    ];

    // 交付配置
    const deliveryConfigs: Omit<DeliveryConfig, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        channel: '短信',
        enabled: true,
        config: {
          provider: '阿里云短信',
          template: '您的医疗票据已开具，点击链接查看：{link}',
          signName: '北京市第一人民医院',
        },
        lastUpdateTime: '2025-01-01 10:00:00',
        operator: '系统管理员',
      },
    ];

    deliveryRecords.forEach(record => deliveryRecordManager.add(record));
    deliveryConfigs.forEach(config => deliveryConfigManager.add(config));
  }

  /**
   * 初始化报表数据
   */
  private static initializeReports(): void {
    // 库存报表
    const inventoryReports: Omit<InventoryReport, 'id' | 'createTime'>[] = [
      {
        invoiceType: '门诊医疗电子票据',
        department: '门诊部',
        initialStock: 1000,
        received: 500,
        issued: 300,
        returned: 10,
        currentStock: 1190,
        reportDate: '2025-01-10',
      },
    ];

    // 开票报表
    const issuingReports: Omit<IssuingReport, 'id' | 'createTime'>[] = [
      {
        department: '门诊部',
        invoiceType: '门诊医疗电子票据',
        issueCount: 150,
        issueAmount: 18750.50,
        reverseCount: 2,
        reverseAmount: 125.00,
        netCount: 148,
        netAmount: 18625.50,
        reportDate: '2025-01-10',
      },
    ];

    inventoryReports.forEach(report => inventoryReportManager.add(report));
    issuingReports.forEach(report => issuingReportManager.add(report));
  }

  /**
   * 初始化系统管理数据
   */
  private static initializeSystemManagement(): void {
    // 角色
    const roles: Omit<Role, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        roleName: '超级管理员',
        roleCode: 'SUPER_ADMIN',
        description: '系统超级管理员，拥有所有权限',
        permissions: ['user:manage', 'role:manage', 'invoice:all', 'report:all'],
        userCount: 1,
        status: '正常',
      },
      {
        roleName: '财务管理员',
        roleCode: 'FINANCE_ADMIN',
        description: '财务部门管理员',
        permissions: ['invoice:manage', 'report:view', 'basic:manage'],
        userCount: 3,
        status: '正常',
      },
    ];

    // 用户
    const users: Omit<User, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        username: 'admin',
        realName: '系统管理员',
        email: '<EMAIL>',
        phone: '13800138001',
        department: '信息科',
        roles: ['超级管理员'],
        status: '正常',
        lastLoginTime: '2025-01-10 09:00:00',
      },
      {
        username: 'finance01',
        realName: '张财务',
        email: '<EMAIL>',
        phone: '13800138002',
        department: '财务科',
        roles: ['财务管理员'],
        status: '正常',
        lastLoginTime: '2025-01-10 08:30:00',
      },
    ];

    // 应用
    const applications: Omit<Application, 'id' | 'createTime' | 'updateTime'>[] = [
      {
        appName: 'HIS系统',
        appCode: 'HIS_SYSTEM',
        appType: '医院信息系统',
        apiKey: 'ak_his_20250101',
        secretKey: 'sk_his_***hidden***',
        status: '正常',
        lastAccessTime: '2025-01-10 09:30:00',
        description: '医院信息系统对接',
      },
    ];

    roles.forEach(role => roleManager.add(role));
    users.forEach(user => userManager.add(user));
    applications.forEach(app => applicationManager.add(app));
  }

  /**
   * 初始化取票小程序数据
   */
  private static initializeMiniProgram(): void {
    // 小程序配置
    const miniProgramConfigs: Omit<MiniProgramConfig, 'id' | 'createTime'>[] = [
      {
        name: '北京市第一人民医院取票小程序',
        description: '患者可通过扫描二维码快速获取医疗电子票据',
        qrCodeUrl: 'https://example.com/qr/hospital001',
        accessUrl: 'https://miniprogram.example.com/hospital001',
        logo: '/logo.png',
        primaryColor: '#1890ff',
        backgroundColor: '#f0f2f5',
        welcomeText: '欢迎使用北京市第一人民医院电子票据查询系统',
        contactInfo: '客服电话：010-12345678',
        status: '已启用',
        updateTime: '2025-01-10 09:00:00',
      },
    ];

    // 访问日志
    const accessLogs: Omit<AccessLog, 'id' | 'createTime'>[] = [
      {
        patientName: '张三',
        patientPhone: '138****8001',
        accessTime: '2025-01-10 09:30:00',
        accessType: '微信扫码',
        invoiceCount: 2,
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        ipAddress: '*************',
      },
    ];

    miniProgramConfigs.forEach(config => miniProgramConfigManager.add(config));
    accessLogs.forEach(log => accessLogManager.add(log));
  }
}
