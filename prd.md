# 医疗电子票据平台 - 产品需求文档（PRD）

## 1. 文档基本信息

| 项目         | 内容                                                                             |
| :----------- | :------------------------------------------------------------------------------- |
| **产品名称** | 医疗电子票据平台                                                                 |
| **PRD 版本** | V1.0                                                                             |
| **编写日期** | 2025 年 07 月 10 日                                                              |
| **文档目的** | 明确医疗电子票据平台的功能需求、非功能需求等，为开发、测试、设计等环节提供依据   |
| **预期读者** | 产品经理、开发工程师、测试工程师、UI/UX 设计师、项目管理人员、医疗机构相关负责人 |
| **修订历史** | 版本 V1.0：初始版本                                                              |

## 2. 产品概述

### 2.1 产品背景与目标

- **背景**：为响应国家关于电子票据推广应用的政策要求，解决医疗机构传统纸质票据管理效率低下、易出错、成本高、患者获取不便等问题，实现医疗票据全流程电子化管理，提升医疗机构财务管理水平和服务质量，特开发本医疗电子票据平台。
- **目标**：构建一个集医疗电子票据（及其他相关票据）的申领、开具、管理、存档、交付、查询、统计分析于一体的综合性平台，实现医疗机构票据管理的规范化、电子化、高效化，为缴款人（患者）提供便捷的电子票据获取渠道。

### 2.2 产品定位

本平台是面向各级医疗机构，提供医疗电子票据（门诊/住院）及非税、往来、捐赠等票据全生命周期管理的专业信息化系统，是医疗机构财务管理和服务患者的重要支撑工具。

## 3. 用户角色

| 角色                    | 描述                                                                                     |
| :---------------------- | :--------------------------------------------------------------------------------------- |
| 医疗机构财务人员        | 负责票据的申领、分发、申退、审验、销毁、开具（部分手工开票场景）、查询、报表统计等操作。 |
| 医疗机构系统管理员      | 负责平台用户管理、角色管理、权限配置、应用接入管理等系统级设置。                         |
| 医疗机构 HIS 系统管理员 | 协助完成 HIS 系统与本平台的对接工作。                                                    |
| 缴款人（患者）          | 通过平台提供的渠道获取、查询电子票据。                                                   |
| 财政部门相关人员        | （间接用户）通过财政系统与本平台进行数据交互，对医疗机构票据使用进行监管。               |

## 4. 核心功能需求

### 4.1 基础信息管理模块

#### 4.1.1 功能概述

该模块是医疗电子票据平台的基础，负责维护医疗机构的核心基础信息，支持与财政系统的数据同步及本地自主维护。

#### 4.1.2 功能点详情

- **财政基础信息获取**
  - 功能描述：通过与财政系统对接，自动获取或手动导入医疗机构的单位基础信息，如单位名称、统一社会信用代码、单位地址、联系人、联系方式等。
  - 触发时机：系统初始化、财政信息更新时。
- **医疗基础信息本地维护**
  - 功能描述：允许医疗机构在平台内维护与医疗业务相关的基础信息，如科室信息、收费项目信息（可与 HIS 同步）、院区信息等。
  - 操作权限：系统管理员或指定有权限的财务人员。
  - 功能：新增、编辑、删除、查询、导入、导出医疗基础信息。

### 4.2 票据管理模块

#### 4.2.1 功能概述

对纸质票据和电子票据的全生命周期进行管理，确保票据流转的规范性和可追溯性。

#### 4.2.2 功能点详情

- **票据申领**
  - 功能描述：医疗机构向财政部门申请所需种类和数量的票据（纸质/电子）。
  - 操作流程：填写申领单（选择票据类型、数量等）→ 提交财政 → 接收财政发放的票据（电子票据直接入库，纸质票据登记入库）。
  - 支持：申领单的保存、提交、撤销、查询。
- **票据分发**
  - 功能描述：医疗机构内部（如总院向分院、财务科向收费处）进行票据的分发管理。
  - 操作：选择分发票据类型、数量、接收部门/人员 → 确认分发 → 接收方确认。
  - 记录：详细记录分发信息，包括分发时间、数量、经手人等。
- **票据申退**
  - 功能描述：当票据出现剩余、作废或其他原因需要退回时，向财政部门或上级分发单位申请退回。
  - 操作：填写申退单（票据类型、数量、申退原因）→ 提交 → 等待审批 → 完成退库。
- **票据审验**
  - 功能描述：对使用完毕的票据存根（纸质）或电子票据使用记录进行校验核对，确保票据使用的合规性。
  - 内容：核对开票金额、开票数量、作废数量等与实际使用情况是否一致。
  - 支持：生成审验报告。
- **票据销毁**
  - 功能描述：对已过保管期限且符合销毁条件的纸质票据存根，按照规定流程进行销毁登记和管理。
  - 流程：提交销毁申请 → 审批 → 执行销毁 → 记录销毁信息（时间、地点、监销人等）。
  - 电子票据一般不涉及物理销毁，主要是归档管理。
- **票据库存管理**
  - 功能描述：实时展示各类票据的库存数量（入库、出库、结存），包括本级机构及下级机构（如有）。
  - 提醒：当票据库存低于设定阈值时，进行预警提示。

### 4.3 票据开具模块

#### 4.3.1 功能概述

该模块是平台的核心，实现与 HIS 系统对接开具医疗电子票据，并支持其他类型票据的开具，覆盖医院全业务、全口径开票需求。

#### 4.3.2 功能点详情

- **医疗电子票据开具（对接 HIS 系统）**
  - 功能描述：与 HIS 系统实时对接，获取门诊/住院患者的缴费信息，自动或半自动生成医疗电子票据。
  - 开票触发：患者完成缴费后，HIS 系统推送缴费数据至本平台，平台生成电子票据。
  - 票据信息：包含票据代码、票据号码、开票日期、缴款人信息、缴费项目明细、金额、开票单位信息、校验码等。
- **票据冲红**
  - 功能描述：当已开具的电子票据出现错误（如金额错误、患者信息错误等）且符合冲红条件时，对原票据进行冲红操作，并生成对应红字票据。
  - 限制：需注明冲红原因，且通常需对应原票据信息，部分冲红需支持。
  - 流程：发起冲红申请 → （可选）审批 → 执行冲红 → 生成红字票据。
- **票据打印**
  - 功能描述：支持电子票据的纸质打印，可打印全票面信息或简化版信息（带校验码）。
  - 场景：患者需要纸质票据时，可在医院自助机、收费窗口打印，或通过取票小程序、APP 自行打印。
- **票据查询**
  - 功能描述：根据票据号码、患者姓名、身份证号、就诊号、缴费日期等条件查询已开具的医疗电子票据。
- **非税、往来、捐赠票据开具**
  - 功能描述：支持在平台内手动录入信息开具非税收入票据、往来结算票据、捐赠票据。
  - 操作：选择票据类型 → 填写开票信息（缴款人、金额、事由等）→ 确认开具。
  - 支持：开票信息的保存、草稿、预览。

### 4.4 票据存档模块

#### 4.4.1 功能概述

负责电子票据的集中存储与管理，确保电子票据的安全性、完整性和可查阅性。

#### 4.4.2 功能点详情

- **电子票据下载与存档**
  - 功能描述：从财政电子票据系统下载已开具的电子票据（XML/PDF 格式），并在平台本地进行结构化或非结构化存储。
  - 触发：票据开具成功后自动同步或定时批量下载。
  - 存储内容：电子票据文件、票据关键信息（数据库记录）。
- **电子票据查阅与交付支持**
  - 功能描述：医疗机构内部人员可在平台内查阅已存档的电子票据详情及原始文件。
  - 支持：为票据交付模块提供数据支持，可直接调取存档的电子票据进行交付。

### 4.5 票据交付模块

#### 4.5.1 功能概述

通过多种渠道将电子票据便捷地交付给缴款人，提升缴款人获取票据的体验。

#### 4.5.2 功能点详情

- **多渠道交付**
  - **短信交付**：患者缴费后，平台自动向其预留的手机号发送包含电子票据查看/下载链接的短信。
  - **单位 APP 交付**：将电子票据信息推送至医疗机构官方 APP 的“我的票据”模块，患者登录后可查看。
  - **互联网渠道交付**：
    - 医疗机构官方网站：提供票据查询入口。
    - 政务服务平台/医保平台：对接相关公共服务平台，实现票据共享查阅。
  - **取票小程序交付**：通过专用二维码取票小程序，患者扫码后可获取票据（详见 4.8）。
- **交付记录查询**
  - 功能描述：记录每笔电子票据的交付渠道、交付时间、交付状态（成功/失败），方便追溯。

### 4.6 综合报表查询模块

#### 4.6.1 功能概述

提供多维度、多层次的票据统计分析报表，为医疗机构财务管理和决策提供数据支持。

#### 4.6.2 功能点详情

- **库存情况查询**
  - 功能描述：按票据类型、时间段、机构（本级及下级）查询票据的期初库存、申领数量、分发数量、使用数量、作废数量、退回数量、期末库存等信息。
  - 展示形式：列表、柱状图、饼图等。
  - 支持：报表导出（Excel/PDF）、打印。
- **开票情况查询**
  - 功能描述：按票据类型、开票日期、收费项目、科室、院区、金额区间等条件统计开票数量、开票金额、冲红数量、冲红金额等。
  - 细分：门诊开票统计、住院开票统计、非税/往来/捐赠票据开票统计。
  - 展示形式：明细表、汇总表、趋势图、对比图等。
  - 支持：报表导出、打印、自定义报表条件保存。

### 4.7 系统管理模块

#### 4.7.1 功能概述

负责平台的用户、权限及外部系统接入的管理，保障系统安全稳定运行。

#### 4.7.2 功能点详情

- **用户管理**
  - 功能：新增、编辑、删除、查询用户账号，重置密码，启用/禁用用户。
  - 用户信息：用户名、密码（加密存储）、姓名、所属部门、联系方式、角色等。
- **角色管理**
  - 功能：自定义角色，为角色分配权限。
  - 预设角色：如超级管理员、财务管理员、票据操作员、报表查看员等。
- **权限配置**
  - 功能：基于 RBAC（角色基础访问控制）模型，对菜单权限、按钮权限、数据权限进行精细化配置。
  - 操作：为角色勾选相应权限。
- **应用接入管理**
  - 功能：管理与本平台对接的外部业务系统（如 HIS 系统）。
  - 包括：对接系统信息登记、接口密钥管理、接入状态监控、访问日志记录。

### 4.8 取票小程序模块

#### 4.8.1 功能概述

为医疗机构定制开发的，供缴款人快速获取电子票据的专用二维码小程序。

#### 4.8.2 功能点详情

- **二维码生成与展示**
  - 功能描述：平台为医疗机构生成唯一的取票小程序二维码，可张贴在收费窗口、自助缴费机、医院导览处等位置。
- **票据查询与获取**
  - 功能描述：患者通过微信/支付宝等扫码工具扫描二维码，进入小程序。
  - 操作：患者输入手机号、身份证号、就诊号等信息进行身份验证 → 系统查询该患者名下的电子票据 → 患者可查看、下载（PDF）、打印票据。
- **个性化配置**
  - 功能描述：支持根据医疗机构的需求，在小程序中展示机构 LOGO、名称、客服信息等，可定制部分页面样式和交互流程。

## 5. 非功能需求

### 5.1 性能需求

- **响应时间**：页面加载时间 ≤3 秒，简单查询操作响应时间 ≤2 秒，复杂报表生成响应时间 ≤10 秒。
- **并发处理**：支持至少[X]个用户同时在线操作（X 根据医疗机构规模设定，如 50-200），峰值时可承受[Y]笔/分钟的开票请求。
- **数据存储**：支持至少[Z]年的电子票据数据存储（Z 符合档案保管期限要求）。

### 5.2 安全需求

- **数据传输安全**：所有数据传输（尤其是与财政系统、HIS 系统、用户端的交互）需采用 HTTPS 等加密协议。
- **数据存储安全**：敏感数据（如用户密码）需加密存储，数据库定期备份，支持数据恢复。
- **访问安全**：采用强密码策略，支持登录验证码，重要操作需二次验证，记录详细操作日志（谁、何时、做了什么操作）。
- **权限控制**：严格的权限隔离，确保用户只能访问和操作其权限范围内的功能和数据。
- **防攻击**：具备一定的防 SQL 注入、XSS 攻击、CSRF 攻击等能力。

### 5.3 可靠性需求

- **系统可用性**：年度平均可用性 ≥99.9%（允许计划性停机维护）。
- **故障恢复**：系统发生故障后，应能在[X]小时内恢复正常运行，数据不丢失。
- **容错性**：对用户的误操作有提示和限制，接口调用失败时有重试机制和错误提示。

### 5.4 易用性需求

- **界面设计**：遵循直观、简洁的设计原则，操作流程符合用户习惯，提供清晰的导航和帮助提示。
- **操作便捷**：减少不必要的操作步骤，常用功能提供快捷入口，支持批量操作。
- **兼容性**：适配主流浏览器（Chrome、Edge、Firefox、IE11+等）。

### 5.5 可扩展性需求

- **架构设计**：采用模块化、松耦合架构，便于后续功能扩展和升级。
- **接口开放**：预留标准化接口，方便与其他系统（如财务系统、ERP 系统）进行集成。
- **性能扩展**：支持通过增加服务器节点等方式提升系统处理能力。

### 5.6 兼容性需求

- 与财政部门电子票据管理系统兼容，遵循其数据标准和接口规范。
- 与主流 HIS 系统有成熟的对接方案或接口适配能力。
- 取票小程序兼容主流的微信、支付宝等扫码工具及版本。

## 6. 系统接口需求

### 6.1 与财政电子票据管理系统接口

- **功能**：基础信息同步、票据申领/分发/申退数据交互、电子票据下载、开票数据上传（如需）。
- **接口类型**：Web Service、REST API 等（根据财政规范）。
- **数据格式**：XML、JSON 等。

### 6.2 与 HIS 系统接口

- **功能**：获取门诊/住院患者缴费信息（患者基本信息、收费项目、金额、就诊号等）、向 HIS 系统返回票据开具结果（票据号码、状态等）。
- **接口类型**：数据库直连、中间表、REST API、消息队列等。
- **实时性**：要求高实时性，确保缴费后能及时开具票据。

### 6.3 与医疗机构 APP/官网接口

- **功能**：向 APP/官网推送或提供电子票据数据，供患者查询。
- **接口类型**：REST API。

### 6.4 与短信平台接口

- **功能**：调用短信接口发送票据交付短信。
- **接口类型**：第三方短信服务商提供的 API。

## 7. 运行环境需求

### 7.1 服务器端

- 操作系统：Windows Server [版本] 或 Linux（如 CentOS [版本]）
- 数据库：MySQL [版本] / Oracle [版本] / SQL Server [版本]
- 应用服务器：Tomcat [版本] / Nginx [版本] 等
- 硬件配置：根据并发量和数据量确定（如 CPU、内存、硬盘空间）

### 7.2 客户端

- 操作系统：Windows 7 及以上，macOS [版本]及以上
- 浏览器：Chrome [版本]及以上，Edge [版本]及以上，Firefox [版本]及以上，IE 11 及以上
- 网络环境：稳定的互联网或局域网连接

## 8. 约束与假设

### 8.1 约束

- 必须遵循国家及地方关于电子票据管理的法律法规和财政部门的相关规定。
- 系统开发需在[项目周期]内完成。
- 接口开发需符合对接系统（财政、HIS）的规范，若对接系统有变动，可能需要调整。

### 8.2 假设

- 医疗机构已具备基本的网络环境和硬件设备。
- 财政电子票据管理系统提供稳定的接口和数据支持。
- HIS 系统能够提供符合要求的缴费数据。
- 患者普遍具备使用智能手机扫码、接收短信、使用 APP 的基本能力。

## 9. 其他需求

- **帮助文档**：提供详细的用户操作手册、管理员手册、接口开发手册。
- **培训支持**：为医疗机构用户提供系统使用培训。
- **日志管理**：系统需记录详细的操作日志、接口调用日志、错误日志，便于问题排查和审计。

## 10. 附录

- 术语解释：如电子票据、冲红、HIS（医院信息系统）等。
- 参考资料：相关政策文件、财政接口规范等。

---

**备注**：本 PRD 为初步版本，后续可根据实际需求调研和讨论进行补充与完善。
