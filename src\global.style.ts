import { createStyles } from "antd-style";

const useStyles = createStyles(() => {
  return {
    colorWeak: {
      filter: "invert(80%)",
    },
    "ant-layout": {
      minHeight: "100vh",
    },
    "ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed": {
      left: "unset",
    },
    canvas: {
      display: "block",
    },
    body: {
      textRendering: "optimizeLegibility",
      WebkitFontSmoothing: "antialiased",
      MozOsxFontSmoothing: "grayscale",
    },
    "ul,ol": {
      listStyle: "none",
    },
    // 表格响应式处理 - 扩展到更大屏幕尺寸
    "@media(max-width: 1200px)": {
      ".ant-table-wrapper": {
        overflowX: "auto",
      },
      ".ant-table": {
        minWidth: "max-content",
      },
    },
    "@media(max-width: 768px)": {
      ".ant-table": {
        width: "100%",
        overflowX: "auto",
        "&-thead > tr, &-tbody > tr": {
          "> th, > td": {
            whiteSpace: "nowrap",
            "> span": {
              display: "block",
            },
          },
        },
      },
      // ProTable 特殊处理
      ".ant-pro-table": {
        ".ant-table-wrapper": {
          overflowX: "auto",
        },
      },
    },
    // 小屏幕下的进一步优化
    "@media(max-width: 576px)": {
      ".ant-table": {
        fontSize: "12px",
        "&-thead > tr > th, &-tbody > tr > td": {
          padding: "8px 4px",
        },
      },
    },
  };
});

export default useStyles;
