// 本地存储工具类
import { message } from "antd";

// 存储键名常量
export const STORAGE_KEYS = {
  // 基础信息管理
  ORGANIZATION_INFO: "medical_invoice_organization_info",
  DEPARTMENT_INFO: "medical_invoice_department_info",
  CHARGE_ITEM_INFO: "medical_invoice_charge_item_info",

  // 票据管理
  INVOICE_APPLICATION: "medical_invoice_application",
  INVOICE_DISTRIBUTION: "medical_invoice_distribution",
  INVOICE_INVENTORY: "medical_invoice_inventory",

  // 票据开具
  MEDICAL_INVOICE: "medical_invoice_medical",
  NON_TAX_INVOICE: "medical_invoice_non_tax",

  // 票据存档
  ARCHIVED_INVOICE: "medical_invoice_archived",
  ARCHIVE_TASK: "medical_invoice_archive_task",

  // 票据交付
  DELIVERY_RECORD: "medical_invoice_delivery_record",
  DELIVERY_CONFIG: "medical_invoice_delivery_config",

  // 综合报表
  INVENTORY_REPORT: "medical_invoice_inventory_report",
  ISSUING_REPORT: "medical_invoice_issuing_report",

  // 系统管理
  USERS: "medical_invoice_users",
  ROLES: "medical_invoice_roles",
  APPLICATIONS: "medical_invoice_applications",

  // 取票小程序
  MINI_PROGRAM_CONFIG: "medical_invoice_mini_program_config",
  ACCESS_LOG: "medical_invoice_access_log",
};

/**
 * 本地存储管理类
 */
export class LocalStorageManager {
  /**
   * 保存数据到localStorage
   */
  static setItem<T>(key: string, data: T): boolean {
    try {
      const jsonString = JSON.stringify(data);
      localStorage.setItem(key, jsonString);
      return true;
    } catch (error) {
      console.error("保存数据到localStorage失败:", error);
      message.error("数据保存失败");
      return false;
    }
  }

  /**
   * 从localStorage获取数据
   */
  static getItem<T>(key: string, defaultValue: T[] = []): T[] {
    try {
      const jsonString = localStorage.getItem(key);
      if (jsonString) {
        return JSON.parse(jsonString);
      }
      return defaultValue as T[];
    } catch (error) {
      console.error("从localStorage获取数据失败:", error);
      return defaultValue as T[];
    }
  }

  /**
   * 删除localStorage中的数据
   */
  static removeItem(key: string): boolean {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error("删除localStorage数据失败:", error);
      return false;
    }
  }

  /**
   * 清空所有医疗票据相关数据
   */
  static clearAll(): boolean {
    try {
      Object.values(STORAGE_KEYS).forEach((key) => {
        localStorage.removeItem(key);
      });
      message.success("所有数据已清空");
      return true;
    } catch (error) {
      console.error("清空数据失败:", error);
      message.error("清空数据失败");
      return false;
    }
  }

  /**
   * 获取存储使用情况
   */
  static getStorageInfo(): { used: number; total: number; percentage: number } {
    try {
      let used = 0;
      Object.values(STORAGE_KEYS).forEach((key) => {
        const data = localStorage.getItem(key);
        if (data) {
          used += new Blob([data]).size;
        }
      });

      // localStorage通常限制为5MB
      const total = 5 * 1024 * 1024;
      const percentage = (used / total) * 100;

      return { used, total, percentage };
    } catch (error) {
      console.error("获取存储信息失败:", error);
      return { used: 0, total: 0, percentage: 0 };
    }
  }
}

/**
 * 数据操作基类
 */
export class BaseDataManager<
  T extends { id: string; createTime?: string; updateTime?: string }
> {
  private storageKey: string;

  constructor(storageKey: string) {
    this.storageKey = storageKey;
  }

  /**
   * 获取所有数据
   */
  getAll(): T[] {
    return LocalStorageManager.getItem<T>(this.storageKey, []);
  }

  /**
   * 根据ID获取单条数据
   */
  getById(id: string): T | undefined {
    const data = this.getAll();
    return data.find((item) => item.id === id);
  }

  /**
   * 添加数据
   */
  add(item: Omit<T, "id" | "createTime" | "updateTime">): T {
    const data = this.getAll();
    const newItem = {
      ...item,
      id: this.generateId(),
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
    } as T;

    data.push(newItem);
    LocalStorageManager.setItem(this.storageKey, data);
    return newItem;
  }

  /**
   * 更新数据
   */
  update(id: string, updates: Partial<T>): T | null {
    const data = this.getAll();
    const index = data.findIndex((item) => item.id === id);

    if (index === -1) {
      return null;
    }

    data[index] = {
      ...data[index],
      ...updates,
      updateTime: new Date().toISOString(),
    };

    LocalStorageManager.setItem(this.storageKey, data);
    return data[index];
  }

  /**
   * 删除数据
   */
  delete(id: string): boolean {
    const data = this.getAll();
    const filteredData = data.filter((item) => item.id !== id);

    if (filteredData.length === data.length) {
      return false; // 没有找到要删除的项
    }

    LocalStorageManager.setItem(this.storageKey, filteredData);
    return true;
  }

  /**
   * 批量删除数据
   */
  batchDelete(ids: string[]): number {
    const data = this.getAll();
    const filteredData = data.filter((item) => !ids.includes(item.id));
    const deletedCount = data.length - filteredData.length;

    LocalStorageManager.setItem(this.storageKey, filteredData);
    return deletedCount;
  }

  /**
   * 分页查询
   */
  paginate(params: {
    current?: number;
    pageSize?: number;
    sorter?: Record<string, any>;
    filter?: Record<string, any>;
  }): {
    data: T[];
    total: number;
    current: number;
    pageSize: number;
  } {
    let data = this.getAll();

    // 应用过滤器
    if (params.filter) {
      data = this.applyFilters(data, params.filter);
    }

    // 应用排序
    if (params.sorter) {
      data = this.applySorting(data, params.sorter);
    }

    const total = data.length;
    const current = params.current || 1;
    const pageSize = params.pageSize || 10;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    return {
      data: data.slice(startIndex, endIndex),
      total,
      current,
      pageSize,
    };
  }

  /**
   * 搜索数据
   */
  search(keyword: string, fields: (keyof T)[]): T[] {
    const data = this.getAll();
    const lowerKeyword = keyword.toLowerCase();

    return data.filter((item) => {
      return fields.some((field) => {
        const value = item[field];
        if (typeof value === "string") {
          return value.toLowerCase().includes(lowerKeyword);
        }
        return false;
      });
    });
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 应用过滤器
   */
  private applyFilters(data: T[], filters: Record<string, any>): T[] {
    return data.filter((item) => {
      return Object.entries(filters).every(([key, value]) => {
        if (value === undefined || value === null || value === "") {
          return true;
        }

        const itemValue = (item as any)[key];
        if (Array.isArray(value)) {
          return value.includes(itemValue);
        }

        if (typeof value === "string") {
          return String(itemValue).toLowerCase().includes(value.toLowerCase());
        }

        return itemValue === value;
      });
    });
  }

  /**
   * 应用排序
   */
  private applySorting(data: T[], sorter: Record<string, any>): T[] {
    const [field, order] = Object.entries(sorter)[0] || [];
    if (!field || !order) {
      return data;
    }

    return [...data].sort((a, b) => {
      const aValue = (a as any)[field];
      const bValue = (b as any)[field];

      let result = 0;
      if (aValue < bValue) result = -1;
      else if (aValue > bValue) result = 1;

      return order === "descend" ? -result : result;
    });
  }

  /**
   * 导出数据
   */
  export(): string {
    const data = this.getAll();
    return JSON.stringify(data, null, 2);
  }

  /**
   * 导入数据
   */
  import(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      if (Array.isArray(data)) {
        LocalStorageManager.setItem(this.storageKey, data);
        return true;
      }
      return false;
    } catch (error) {
      console.error("导入数据失败:", error);
      return false;
    }
  }

  /**
   * 清空所有数据
   */
  clear(): boolean {
    return LocalStorageManager.setItem(this.storageKey, []);
  }

  /**
   * 获取数据统计信息
   */
  getStats(): {
    total: number;
    createdToday: number;
    updatedToday: number;
  } {
    const data = this.getAll();
    const today = new Date().toISOString().split("T")[0];

    return {
      total: data.length,
      createdToday: data.filter((item) => item.createTime?.startsWith(today))
        .length,
      updatedToday: data.filter((item) => item.updateTime?.startsWith(today))
        .length,
    };
  }
}

// 导出各模块的数据管理器实例
export const organizationManager = new BaseDataManager(
  STORAGE_KEYS.ORGANIZATION_INFO
);
export const departmentManager = new BaseDataManager(
  STORAGE_KEYS.DEPARTMENT_INFO
);
export const chargeItemManager = new BaseDataManager(
  STORAGE_KEYS.CHARGE_ITEM_INFO
);
export const invoiceApplicationManager = new BaseDataManager(
  STORAGE_KEYS.INVOICE_APPLICATION
);
export const invoiceDistributionManager = new BaseDataManager(
  STORAGE_KEYS.INVOICE_DISTRIBUTION
);
export const invoiceInventoryManager = new BaseDataManager(
  STORAGE_KEYS.INVOICE_INVENTORY
);
export const medicalInvoiceManager = new BaseDataManager(
  STORAGE_KEYS.MEDICAL_INVOICE
);
export const nonTaxInvoiceManager = new BaseDataManager(
  STORAGE_KEYS.NON_TAX_INVOICE
);
export const archivedInvoiceManager = new BaseDataManager(
  STORAGE_KEYS.ARCHIVED_INVOICE
);
export const archiveTaskManager = new BaseDataManager(
  STORAGE_KEYS.ARCHIVE_TASK
);
export const deliveryRecordManager = new BaseDataManager(
  STORAGE_KEYS.DELIVERY_RECORD
);
export const deliveryConfigManager = new BaseDataManager(
  STORAGE_KEYS.DELIVERY_CONFIG
);
export const inventoryReportManager = new BaseDataManager(
  STORAGE_KEYS.INVENTORY_REPORT
);
export const issuingReportManager = new BaseDataManager(
  STORAGE_KEYS.ISSUING_REPORT
);
export const userManager = new BaseDataManager(STORAGE_KEYS.USERS);
export const roleManager = new BaseDataManager(STORAGE_KEYS.ROLES);
export const applicationManager = new BaseDataManager(
  STORAGE_KEYS.APPLICATIONS
);
export const miniProgramConfigManager = new BaseDataManager(
  STORAGE_KEYS.MINI_PROGRAM_CONFIG
);
export const accessLogManager = new BaseDataManager(STORAGE_KEYS.ACCESS_LOG);
